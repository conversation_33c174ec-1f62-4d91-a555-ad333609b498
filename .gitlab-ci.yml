# GitLab CI/CD 配置文件 - 多架构Docker镜像构建
# 支持构建 amd64 和 arm64 架构的Docker镜像

stages:
  - build_docker

variables:
  # Docker镜像相关变量
  IMAGE_NAME: "service-platform"
  IMAGE_TAG: "${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

build_docker:
  stage: build_docker
  image: *************/docker:24-dind
  services:
    - *************/docker:24-dind
  before_script:
    # 检查Docker buildx是否支持多架构
    - |
      if ! docker buildx inspect | grep -q "linux/amd64"; then
        echo "ERROR: amd64 platform not supported"
        exit 1
      fi
      if ! docker buildx inspect | grep -q "linux/arm64"; then
        echo "ERROR: arm64 platform not supported"
        exit 1
      fi
    # 登录到Docker仓库
    - echo "$DOCKER_PASSWORD" | docker login $DOCKER_IMAGE_SERVER -u "$DOCKER_USERNAME" --password-stdin
    # 创建Docker buildx上下文
    - docker context create builder-context || true
    # 设置Docker buildx
    - docker buildx create --name multiarchbuilder --driver docker-container --buildkitd-config buildkitd.toml --bootstrap --use builder-context
    - docker buildx inspect multiarchbuilder
  script:
    # 构建多架构镜像
    - |
      docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --build-arg VERSION="${IMAGE_TAG}" \
        -t "${DOCKER_IMAGE_SERVER}/${IMAGE_NAME}:${IMAGE_TAG}" \
        -t "${DOCKER_IMAGE_SERVER}/${IMAGE_NAME}:latest" \
        -f Dockerfile.supervisor \
        --push .
    # 验证镜像
    - docker buildx imagetools inspect "${DOCKER_IMAGE_SERVER}/${IMAGE_NAME}:${IMAGE_TAG}"
  after_script:
    # 清理buildx构建器
    - docker buildx rm multiarch-builder || true
    # 登出Docker仓库
    - docker logout $DOCKER_IMAGE_SERVER
  rules:
    # 仅在标签推送时运行
    - if: $CI_COMMIT_TAG
    # 手动触发
    - when: manual
      allow_failure: true
  tags:
    - docker
