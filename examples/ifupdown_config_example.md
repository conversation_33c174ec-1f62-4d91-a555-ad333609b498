# ifupdown 网络配置使用示例

## 概述

本示例展示如何使用 ifupdown 功能在 Docker 容器中配置宿主机网络。ifupdown 是传统的 Debian/Ubuntu 网络配置工具。

## 前提条件

1. Docker 容器需要以特权模式运行
2. 宿主机的 `/sys` 目录需要挂载到容器的 `/host/sys`
3. 容器需要安装 `nsenter` 工具
4. 宿主机使用 ifupdown 网络管理工具

## Docker 运行命令示例

```bash
docker run -d \
  --privileged \
  --pid=host \
  --network=host \
  -v /sys:/host/sys:ro \
  -v /proc:/host/proc:ro \
  -p 8080:8080 \
  your-service-container:latest
```

## ifupdown 配置逻辑

### 1. 配置文件位置
- 配置文件路径：`/etc/network/interfaces.d/<interfaceName>`
- 例如：`/etc/network/interfaces.d/eth0`

### 2. 配置文件格式

#### 静态 IP 配置示例
```bash
# Configuration for eth0
auto eth0
iface eth0 inet static
    address *************
    netmask *************
    gateway ***********
    dns-nameservers ******* ***************
```

#### DHCP 配置示例
```bash
# Configuration for eth1
auto eth1
iface eth1 inet dhcp
```

#### 多 IP 配置示例（使用别名接口）
```bash
# Configuration for eth2
auto eth2
iface eth2 inet static
    address **********
    netmask *************

auto eth2:1
iface eth2:1 inet static
    address **********
    netmask *************
    gateway ********
    dns-nameservers ******* ***************
```

### 3. 实现细节

#### 配置文件生成
1. 使用 `nsenter` 进入宿主机命名空间
2. 使用 `echo` 和 `tee` 命令将配置写入文件
3. 支持覆盖现有配置文件

#### 网络接口重启
1. 使用 `ifdown <interface>` 停止接口
2. 使用 `ifup <interface>` 启动接口
3. 命令格式：`ifdown eth0 && ifup eth0`

## API 调用示例

### 1. 配置静态 IP

```bash
curl -X POST http://localhost:8080/v1/networks/config \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "eth0",
      "dhcp": false,
      "address": "*************",
      "netmask": "*************",
      "gateway": "***********"
    }
  ]'
```

### 2. 配置 DHCP

```bash
curl -X POST http://localhost:8080/v1/networks/config \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "eth1",
      "dhcp": true,
      "address": "",
      "netmask": "",
      "gateway": ""
    }
  ]'
```

### 3. 配置多 IP

```bash
curl -X POST http://localhost:8080/v1/networks/config \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "eth2",
      "dhcp": false,
      "address": "**********",
      "netmask": "*************",
      "gateway": ""
    },
    {
      "name": "eth2",
      "dhcp": false,
      "address": "**********",
      "netmask": "*************",
      "gateway": "********"
    }
  ]'
```

## 特性说明

### 1. 多 IP 支持
- 支持单个网卡配置多个 IP 地址
- 使用别名接口实现（eth0:1, eth0:2 等）
- 第一个 IP 作为主 IP，其他作为别名

### 2. 网关配置
- 以最后一个非空的网关配置为准
- 只在主接口上配置网关

### 3. DNS 配置
- 自动添加 DNS 服务器：*******, ***************
- 只在静态 IP 配置中添加

## 错误处理

### 1. 配置文件写入失败
```
failed to write config to /etc/network/interfaces.d/eth0: <error details>
```

### 2. 网络接口重启失败
```
failed to restart interface eth0: <error details>
```

### 3. 无效的网络接口
```
interface eth0 from config is not a valid physical interface
```

## 注意事项

1. **权限要求**：容器必须以特权模式运行
2. **网络中断**：配置过程中网络接口会短暂中断
3. **配置持久化**：配置会持久化到宿主机文件系统
4. **兼容性**：仅适用于使用 ifupdown 的 Linux 发行版（如 Debian/Ubuntu）
5. **备份建议**：建议在配置前备份现有的网络配置文件

## 故障排除

### 1. 检查网络管理工具
```bash
# 检查是否使用 ifupdown
cat /etc/network/interfaces
```

### 2. 手动测试配置
```bash
# 手动测试接口配置
ifdown eth0 && ifup eth0
```

### 3. 查看配置文件
```bash
# 查看生成的配置文件
cat /etc/network/interfaces.d/eth0
```

### 4. 检查接口状态
```bash
# 查看接口状态
ip addr show eth0
```
