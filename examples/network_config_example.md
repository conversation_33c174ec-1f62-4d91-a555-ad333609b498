# NetworkManager 网络配置使用示例

## 概述

本示例展示如何使用NetworkManager功能在Docker容器中配置宿主机网络。

## 前提条件

1. Docker容器需要以特权模式运行
2. 宿主机的`/sys`目录需要挂载到容器的`/host/sys`
3. 容器需要安装`nsenter`工具

## Docker运行命令示例

```bash
docker run -d \
  --privileged \
  --pid=host \
  --network=host \
  -v /sys:/host/sys:ro \
  -v /proc:/host/proc:ro \
  -p 8080:8080 \
  your-service-container:latest
```

## API调用示例

### 1. 获取当前网络接口信息

```bash
curl -X GET http://localhost:8080/v1/networks/interfaces
```

响应示例：
```json
[
  {
    "name": "enaphyt4i0",
    "dhcp": false,
    "address": "*************",
    "netmask": "*************",
    "gateway": "***********"
  },
  {
    "name": "enaphyt4i1", 
    "dhcp": true,
    "address": "",
    "netmask": "",
    "gateway": ""
  }
]
```

### 2. 配置网络接口

#### 配置静态IP（包含固定IP）

```bash
curl -X POST http://localhost:8080/v1/platform/network \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "enaphyt4i0",
      "dhcp": false,
      "address": "*************",
      "netmask": "*************", 
      "gateway": "***********"
    }
  ]'
```

**配置结果：**
- 固定IP: `***********/24` (根据网卡排序自动生成)
- 用户IP: `*************/24` (用户配置)
- 网关: `***********`

#### 配置DHCP

```bash
curl -X POST http://localhost:8080/v1/platform/network \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "enaphyt4i1",
      "dhcp": true
    }
  ]'
```

### 3. 多网卡配置示例

```bash
curl -X POST http://localhost:8080/v1/platform/network \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "enaphyt4i0",
      "dhcp": false,
      "address": "*************",
      "netmask": "*************",
      "gateway": "***********"
    },
    {
      "name": "enaphyt4i1", 
      "dhcp": false,
      "address": "**********",
      "netmask": "*************"
    }
  ]'
```

**配置结果：**
- `enaphyt4i0`: 固定IP `***********/24` + 用户IP `*************/24`
- `enaphyt4i1`: 固定IP `***********/24` + 用户IP `**********/24`

## 固定IP分配规则

固定IP按照网卡名字符排序分配：

| 网卡名 | 排序位置 | 固定IP |
|--------|----------|--------|
| enaphyt4i0 | 0 | ***********/24 |
| enaphyt4i1 | 1 | ***********/24 |
| eth0 | 2 | ***********/24 |
| eth1 | 3 | ***********/24 |

## 错误处理

### 常见错误及解决方案

1. **网卡不存在**
   ```json
   {
     "message": "interface eth999 from config is not a valid physical interface"
   }
   ```

2. **NetworkManager未运行**
   ```json
   {
     "message": "Could not detect network manager"
   }
   ```

3. **IP地址格式错误**
   ```json
   {
     "message": "invalid netmask 255.255.255.999: invalid netmask format"
   }
   ```

## 日志查看

容器日志会显示详细的配置过程：

```
Detected NetworkManager is active.
Added fixed IP ***********/24 for interface enaphyt4i0
Added user IP *************/24 for interface enaphyt4i0
Modifying existing connection 'Wired connection 1' for device enaphyt4i0
Re-activating connection 'Wired connection 1'...
Network configuration applied successfully
```
