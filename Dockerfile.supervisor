FROM --platform=$BUILDPLATFORM golang:1.21-bookworm AS go-build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ENV GOOS linux
ENV GOPROXY https://goproxy.cn,direct
ENV GO111MODULE on
ENV CGO_ENABLED=0

# 根据目标平台设置GOARCH
RUN case "$TARGETPLATFORM" in \
    "linux/amd64") export GOARCH=amd64 ;; \
    "linux/arm64") export GOARCH=arm64 ;; \
    *) echo "Unsupported platform: $TARGETPLATFORM" && exit 1 ;; \
    esac && echo "GOARCH=$GOARCH" > /tmp/goarch.env

ARG VERSION
WORKDIR /go/src
COPY go-server .
RUN . /tmp/goarch.env && go build -ldflags="-X 'main.VERSION=${VERSION}'" -o skylink main.go

FROM node:16-bookworm AS vue-build
WORKDIR /vue
COPY web /vue/
RUN yarn config set registry https://registry.npmmirror.com/ && \
  yarn && yarn build

FROM debian:12
RUN set -ex; \
  sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources; \
  apt-get update; \
  apt-get install --no-install-recommends -y curl supervisor nginx iproute2 iputils-ping util-linux; \
  apt-get clean; \
  rm -rf /var/lib/apt/lists/*; \
  mkdir -p /var/log/supervisor; \
  mkdir -p /app/images/;
ENV GIN_MODE=release
ENV TZ=Asia/Shanghai
COPY supervisord.conf /etc/supervisord.conf
COPY --from=vue-build /vue/dist /usr/share/nginx/html
WORKDIR /app
COPY --from=go-build /go/src/skylink .
# 拷贝不同架构的docker-compose二进制文件
COPY go-server/docker-compose-linux-x86_64 /tmp/
COPY go-server/docker-compose-linux-aarch64 /tmp/
# 根据目标平台选择对应的docker-compose二进制文件
RUN case "$TARGETPLATFORM" in \
    "linux/amd64") cp /tmp/docker-compose-linux-x86_64 /usr/local/bin/docker-compose ;; \
    "linux/arm64") cp /tmp/docker-compose-linux-aarch64 /usr/local/bin/docker-compose ;; \
    *) echo "Unsupported platform: $TARGETPLATFORM" && exit 1 ;; \
    esac && chmod +x /usr/local/bin/docker-compose && rm -f /tmp/docker-compose-*
COPY go-server/application.yaml .
COPY go-server/model.conf .
COPY go-server/policy.csv .
COPY go-server/gorm.sqlite .
COPY go-server/gorm.sqlite /data/
COPY go-server/images/ /app/images/
# COPY go-server/id_rsa .
# COPY go-server/id_rsa.pub .
# 配置ngix
COPY nginx.conf /etc/nginx/
EXPOSE 8083/tcp
EXPOSE 8082/tcp
# 健康检查
HEALTHCHECK --interval=15s --timeout=3s \
  CMD curl -fs http://localhost:8082/ping || exit 1
VOLUME ["/var/run/docker.sock"]
CMD ["supervisord", "-c", "/etc/supervisord.conf"]
