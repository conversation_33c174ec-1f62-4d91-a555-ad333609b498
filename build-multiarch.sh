#!/bin/bash

# 多架构Docker镜像构建脚本
# 支持 amd64 和 arm64 架构

set -e

# 默认配置
IMAGE_NAME="service-platform"
VERSION="2.7.0"
REGISTRY=""
PUSH=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -n|--name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    -v|--version)
      VERSION="$2"
      shift 2
      ;;
    -r|--registry)
      REGISTRY="$2"
      shift 2
      ;;
    -p|--push)
      PUSH=true
      shift
      ;;
    -h|--help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -n, --name      镜像名称 (默认: service-platform)"
      echo "  -v, --version   版本标签 (默认: 2.7.0)"
      echo "  -r, --registry  镜像仓库地址 (可选)"
      echo "  -p, --push      构建后推送到仓库"
      echo "  -h, --help      显示帮助信息"
      echo ""
      echo "示例:"
      echo "  $0 -n myapp -v 1.0.0 -r registry.example.com -p"
      exit 0
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# 构建完整的镜像标签
if [ -n "$REGISTRY" ]; then
  FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$VERSION"
else
  FULL_IMAGE_NAME="$IMAGE_NAME:$VERSION"
fi

echo "=== 多架构Docker镜像构建 ==="
echo "镜像名称: $FULL_IMAGE_NAME"
echo "支持架构: linux/amd64, linux/arm64"
echo "推送到仓库: $PUSH"
echo ""

# 检查Docker buildx是否可用
if ! docker buildx version >/dev/null 2>&1; then
  echo "错误: Docker buildx 不可用，请确保Docker版本支持buildx"
  exit 1
fi

# 创建并使用buildx构建器
echo "设置buildx构建器..."
BUILDER_NAME="multiarch-builder"

# 检查构建器是否已存在
if ! docker buildx inspect $BUILDER_NAME >/dev/null 2>&1; then
  echo "创建新的buildx构建器: $BUILDER_NAME"
  docker buildx create --name $BUILDER_NAME --driver docker-container --bootstrap
fi

docker buildx use $BUILDER_NAME

# 构建多架构镜像
echo "开始构建多架构镜像..."

BUILD_ARGS="--platform linux/amd64,linux/arm64"
BUILD_ARGS="$BUILD_ARGS --build-arg VERSION=$VERSION"
BUILD_ARGS="$BUILD_ARGS -t $FULL_IMAGE_NAME"
BUILD_ARGS="$BUILD_ARGS -f Dockerfile.supervisor"

if [ "$PUSH" = true ]; then
  BUILD_ARGS="$BUILD_ARGS --push"
  echo "构建并推送镜像: $FULL_IMAGE_NAME"
else
  BUILD_ARGS="$BUILD_ARGS --load"
  echo "构建镜像: $FULL_IMAGE_NAME"
fi

# 执行构建
docker buildx build $BUILD_ARGS .

if [ $? -eq 0 ]; then
  echo ""
  echo "✅ 多架构镜像构建成功!"
  echo "镜像: $FULL_IMAGE_NAME"
  echo "架构: linux/amd64, linux/arm64"
  
  if [ "$PUSH" = true ]; then
    echo "已推送到仓库"
  else
    echo "镜像已保存到本地"
    echo ""
    echo "要推送到仓库，请运行:"
    echo "  $0 -n $IMAGE_NAME -v $VERSION $([ -n "$REGISTRY" ] && echo "-r $REGISTRY") -p"
  fi
else
  echo "❌ 构建失败"
  exit 1
fi