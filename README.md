# Service Container

A containerized service platform with Docker management capabilities.

## 🏗️ 多架构支持

本项目支持构建 **amd64** 和 **arm64** 两种架构的Docker镜像，适用于不同的硬件平台。

### 快速构建多架构镜像

```bash
# 基本构建
./build-multiarch.sh

# 构建并推送到仓库
./build-multiarch.sh -n service-platform -v 2.7.0 -r your-registry.com -p
```

详细的多架构构建指南请参考：[BUILD-MULTIARCH.md](./BUILD-MULTIARCH.md)

# build image

```shell
sudo docker build --build-arg VERSION={tag} --force-rm --progress plain -f Dockerfile.supervisor -t service-platform:{tag} .
```

# run container

```shell
sudo docker run -p 8083:8083 -p 8082:8082 --name service-platform --pid=host --privileged=true --restart=always -e "HOST_PROC=/host/proc" -v /proc/:/host/proc/ -v /sys:/host/sys/ -v /var/run/docker.sock:/var/run/docker.sock -v service-platform_data:/data -d service-platform:{tag}
```

# develop

## live reload

<https://github.com/air-verse/air>

    go install github.com/air-verse/air@latest
    air init //initialize the .air.toml configuration file
    air // run with default config

## swag

<https://github.com/swaggo/swag/blob/master/README_zh-CN.md>

## 添加服务入口规则

    ENV Entrypoint_{index}_NAME="录播运维"  #服务入口名称
    ENV Entrypoint_{index}_PORT=80  #服务入口端口
    ENV Entrypoint_{index}_PATH="/c"       #服务入口URL路径
    ENV Entrypoint_{index}_TAGS="服务端,录播平台"       #服务入口标签数组(多个标签，以英文逗号分隔)
    ENV Entrypoint_{index}_{key}={value}       #服务入口额外的属性元数据
    #例：***********:80/config.html
    #路径path值为"/config.html"

# 程序奔溃

```

#程序结构
--app
    --application.yaml
    --dist               (dir)
    --skylink
```

解决方法：使用ssh连接宿主机，使用命令拷贝相关程序到容器内 `sudo docker cp {程序} service-platform:/app/`，并重启容器 `sudo docker restart service-platform`

# 数据库恢复方式

使用portainer进入服务的终端

执行 `cp /app/gorm.sqlite /data/`
