# IDE 的工作流

在现代软件开发中，我们通常会使用 Monorepo 架构，将前端和后端代码放在同一个项目中。这种架构的好处是可以让我们在一个 IDE 中同时开发和调试前后端代码，而不需要在两个 IDE 实例之间切换。

您需要的是在一个 IDE 窗口内，无缝地对前后端代码进行开发、调试和运行，而不是在两个 IDE 实例之间切换。

核心解决方案是：**使用支持多项目工作区（Workspace）的 IDE，并在一个窗口中管理整个项目。**

下面我以最主流的两款 IDE——**VS Code**和**JetBrains 系列 (GoLand/IntelliJ IDEA)**——为例，为您提供详细的配置步骤。

---

### 方案一：使用 VS Code 的“工作区 (Workspace)”功能（强烈推荐）

VS Code 的工作区功能就是为解决您这个问题而生的。它允许你将多个独立的文件夹添加到一个窗口中，并为每个文件夹提供完整的语言支持。

#### 操作步骤：

1.  **用 VS Code 打开根目录**：
    首先，使用 VS Code 打开你的项目根目录 `my-go-vue-project/`。

    ```bash
    code my-go-vue-project
    ```

    此时，你会在文件浏览器中看到 `backend` 和 `frontend` 两个文件夹，但可能 Go 或 Vue 的插件功能不完全正常。

2.  **创建工作区 (Workspace)**：

    - 在 VS Code 中，点击菜单栏 `File` -> `Add Folder to Workspace...`。
    - 选择 `backend` 文件夹。
    - 再次点击 `File` -> `Add Folder to Workspace...`，选择 `frontend` 文件夹。
    - 现在，你的文件浏览器左侧会清晰地列出 `BACKEND` 和 `FRONTEND` 两个根目录，而不是一个父目录下的子目录。

3.  **保存工作区**：

    - 点击菜单栏 `File` -> `Save Workspace As...`。
    - 在你的项目根目录 `my-go-vue-project/`下，将其保存为 `my-project.code-workspace`。
    - 下次，你只需要直接打开这个 `.code-workspace` 文件，VS Code 就会恢复这个多根目录的布局。

4.  **安装必要的插件**：
    确保你已经安装了：

    - **Go**: Go Team at Google 官方的 `Go` 插件。
    - **Vue**: `Vue Language Features (Volar)` 插件。

5.  **享受丝滑的开发体验**：
    - **统一视图**：在一个窗口内查看和编辑所有前后端文件。
    - **智能提示**：当你在 `backend` 文件夹下的 `.go` 文件中编码时，Go 插件会完美工作。当你在 `frontend` 文件夹下的 `.vue` 文件中编码时，Volar 插件会完美工作。
    - **集成终端**：这是关键！打开 VS Code 的集成终端 ( `Ctrl` + `~` )，然后点击右上角的 **“拆分终端” (Split Terminal)** 图标。
      - **左侧终端**：`cd backend`，然后运行 `air` 或 `go run ./cmd/web/main.go` 启动后端。
      - **右侧终端**：`cd frontend`，然后运行 `npm run dev` 启动前端。

#### 进阶：使用 `tasks.json` 一键启动所有服务

你甚至可以配置一个任务，一键启动前后端所有服务。

1.  在项目根目录创建 `.vscode/tasks.json` 文件。
2.  填入以下内容：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Run Backend (Go)",
      "type": "shell",
      "command": "air", // 或者 "go run ./cmd/web/main.go"
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": [],
      "presentation": {
        "reveal": "always",
        "panel": "new",
        "group": "dev"
      }
    },
    {
      "label": "Run Frontend (Vue)",
      "type": "shell",
      "command": "npm run dev",
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "problemMatcher": [],
      "presentation": {
        "reveal": "always",
        "panel": "new",
        "group": "dev"
      }
    },
    {
      "label": "Start Dev All",
      "dependsOn": ["Run Backend (Go)", "Run Frontend (Vue)"],
      "dependsOrder": "parallel",
      "problemMatcher": []
    }
  ]
}
```

现在，按下 `Ctrl + Shift + P`，输入 `Tasks: Run Task`，选择 `Start Dev All`，VS Code 会自动为你打开两个终端并分别启动前后端服务！

---

### 方案二：使用 JetBrains IDE (GoLand 或 IntelliJ IDEA Ultimate)

JetBrains 的 IDE 同样对 Monorepo 有非常好的支持。

- **最佳选择**: **IntelliJ IDEA Ultimate**。因为它本身就集成了 Go 和 Web 开发的全部功能，是处理这种混合项目的终极利器。
- **可行选择**: **GoLand**。GoLand 也可以很好地处理这种情况，因为它能识别并部分支持 `package.json` 和前端技术。

#### 操作步骤 (以 GoLand 为例)：

1.  **打开项目根目录**：
    使用 GoLand 打开 `my-go-vue-project/` 根目录。GoLand 会自动识别 `go.mod` 并将其设置为 Go 项目。

2.  **配置 Node.js 和 NPM/Yarn**：

    - 进入 `Settings/Preferences` -> `Languages & Frameworks` -> `Node.js`。
    - 确保 GoLand 已经正确识别了你系统中的 Node.js 解释器。

3.  **使用“运行/调试配置 (Run/Debug Configurations)”**：
    这是 JetBrains 系列的精髓。

    - 点击 IDE 右上角的 `Add Configuration...`。
    - **创建后端配置**:
      - 点击 `+` 号，选择 `Go Build`。
      - **Name**: `Backend Server`
      - **Files**: 选择你的 `main.go` 文件 (`backend/cmd/web/main.go`)。
      - **Working directory**: 设置为项目根目录或`backend`目录。
    - **创建前端配置**:
      - 点击 `+` 号，选择 `npm`。
      - **Name**: `Frontend Server`
      - **package.json**: 选择 `frontend/package.json`。
      - **Scripts**: 从下拉菜单中选择 `dev`。
      - **Node interpreter**: 确认 Node.js 解释器已选。
    - **创建组合配置 (Compound)**:
      - 点击 `+` 号，选择 `Compound`。
      - **Name**: `🚀 Start All`
      - 在下方的 `Configurations to run` 中，点击 `+`，添加刚才创建的 `Backend Server` 和 `Frontend Server`。

4.  **一键启动**：
    现在，在 IDE 右上角的配置下拉菜单中选择你创建的 `🚀 Start All`，然后点击旁边的绿色“运行”按钮。IDE 会在一个“运行”面板中同时启动前后端两个服务，每个服务都有自己的标签页，可以查看日志和重启。

### 总结

| 特性         | VS Code 方案                          | JetBrains 方案                               |
| :----------- | :------------------------------------ | :------------------------------------------- |
| **核心机制** | **多根工作区 (Multi-root Workspace)** | **运行/调试配置 (Run/Debug Configurations)** |
| **启动方式** | 拆分集成终端 / `tasks.json`           | 组合配置 (Compound Configuration)            |
| **学习成本** | 较低，配置直观                        | 略高，但功能更强大、集成度更高               |
| **推荐度**   | ⭐⭐⭐⭐⭐ (免费、灵活、生态强大)     | ⭐⭐⭐⭐⭐ (专业、高效、集成体验极致)        |

无论您选择哪种 IDE，**关键思想都是在单个窗口中管理整个项目，并利用 IDE 的内置工具来并行运行和管理多个进程**。这样您就彻底告别了打开多个 IDE 窗口的烦恼，可以像开发一个项目一样，高效地在前后端代码之间穿梭。

希望这些具体的步骤能彻底解决您在开发流程中的不便，让您能更专注于编码本身！
