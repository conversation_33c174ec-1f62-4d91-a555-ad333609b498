# NetworkManager 网络配置功能

## 概述

本功能实现了在Docker容器中使用nsenter对宿主机网络进行配置修改，支持不同的Linux发行版和网络管理工具。

## 支持的网络管理工具

1. **NetworkManager** (已实现) - 使用nmcli命令
2. **systemd-networkd** (预留实现)
3. **传统ifupdown** (已有实现)

## NetworkManager 实现特性

### 自动检测
系统会自动检测宿主机使用的网络管理工具：
- 检查NetworkManager服务状态
- 检查systemd-networkd服务状态  
- 检查传统ifupdown配置文件

### 固定IP地址分配

当DHCP为false时，系统会为每个网卡分配两个IP地址：

1. **固定IP地址**: `10.10.x.10/24`
   - x根据网卡名在所有物理网卡中的字符排序位置决定
   - 例如：有两个网卡`enaphyt4i0`和`enaphyt4i1`
     - `enaphyt4i0`的固定IP为`***********/24`
     - `enaphyt4i1`的固定IP为`***********/24`

2. **用户配置IP地址**: 根据API请求参数设置

### API使用示例

```json
POST /v1/platform/network
{
  "interfaces": [
    {
      "name": "enaphyt4i0",
      "dhcp": false,
      "address": "*************",
      "netmask": "*************",
      "gateway": "***********"
    },
    {
      "name": "enaphyt4i1", 
      "dhcp": true
    }
  ]
}
```

### 配置结果

对于上述配置，`enaphyt4i0`网卡将会有：
- 固定IP: `***********/24`
- 用户IP: `*************/24`
- 网关: `***********`

`enaphyt4i1`网卡将使用DHCP自动获取IP。

## 实现细节

### 核心函数

1. **DetectNetworkManager()**: 检测网络管理工具
2. **ApplyHostNetworkConfig()**: 应用网络配置
3. **generateFixedIP()**: 生成固定IP地址
4. **applyWithNetworkManager()**: 使用NetworkManager应用配置

### NetworkManager命令示例

```bash
# 修改现有连接
nmcli connection modify conn-enaphyt4i0 \
  ipv4.method manual \
  ipv4.addresses "***********/24,*************/24" \
  ipv4.gateway "***********" \
  ipv4.dns "*******,***************"

# 重新激活连接
nmcli connection down conn-enaphyt4i0
nmcli connection up conn-enaphyt4i0
```

## 错误处理

- 验证网卡名是否为物理网卡
- 检查网络管理工具是否可用
- 验证IP地址和子网掩码格式
- 处理连接激活失败的情况

## 日志记录

系统会记录以下信息：
- 检测到的网络管理工具
- 为每个网卡生成的固定IP
- 添加的用户配置IP
- 命令执行结果

## 测试

运行测试：
```bash
cd go-server
go test ./utils -v
```

注意：集成测试需要在实际的容器环境中运行。
