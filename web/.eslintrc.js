module.exports = {
  extends: ["plugin:vue/vue3-recommended", "airbnb-base"],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["vue"],
  parser: "vue-eslint-parser",
  rules: {
    "linebreak-style": [0, "unix"],
    quotes: [0, "single"],
    "max-len": [0, { code: 120 }],
    "import/prefer-default-export": "off",
    "arrow-body-style": "off",
    "vue/html-self-closing": "off",
    "vue/max-attributes-per-line": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/attributes-order": "off",
    "vue/html-closing-bracket-newline": "off",
    "vue/v-slot-style": "off",
    "vue/html-indent": "off",
    "vue/multiline-html-element-content-newline": "off",
    "object-curly-newline": "off",
    "consistent-return": "off",
    "no-unused-vars": "warn",
    "no-param-reassign": "off",
    "no-restricted-globals": "off",
    "no-else-return": "off",
  },
};
