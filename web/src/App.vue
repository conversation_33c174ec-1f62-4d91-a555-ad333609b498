<template>
  <el-config-provider :locale="locale">
    <router-view />
  </el-config-provider>
</template>

<script>
import zhCn from "element-plus/es/locale/lang/zh-cn"; // 控件中文

export default {
  name: "App",
  data() {
    return {
      locale: zhCn,
    };
  },
  created() {
    this.changeFavicon();
  },
  methods: {
    // 插入favicon.icon的方法
    changeFavicon() {
      const $favicon = document.createElement("link");
      $favicon.rel = "icon";
      $favicon.href = `${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.pictures.favicon.url}`;
      document.head.appendChild($favicon);
    },
  },
};
</script>
<style>
body {
  margin: 0;
  padding: 0;
}
</style>
