/**
 * 网站配置文件
 */
const config = {
  appName: "统一服务器",
  appLogo: "/assets/logo.png",
  // 后端地址
  serverBaseURL: import.meta.env.MODE === "development" ? "http://*************:8084" : `${location.protocol}//${location.hostname}:8082`, // 后端服务的端口
  websocketBaseURL: import.meta.env.MODE === "development" ? "ws://*************:8084" : `ws://${location.hostname}:8082`, // 后端服务的端口
  webBaseURL: ``, // 前端服务的端口
  serverApi: {
    // 登录
    loginServer: "/login",
    refreshToken: "/auth/refresh_token", // 刷新令牌
    // 版本信息等
    about: "/about",
    /* 容器 */
    containerCreate: "/v1/containers/create", // 创建容器
    containerStart: "/v1/containers/%s/start", // 启动容器
    containerStop: "/v1/containers/%s/stop", // 停止容器
    containerRestart: "/v1/containers/%s/restart", // 重启容器
    containerRename: "/v1/containers/%s/rename", // 修改容器名
    containerDel: "/v1/containers/%s", // 删除容器
    containers: "/v1/containers/json", // 获取容器
    containerInspect: "/v1/containers/%s/json", // 容器信息
    containerBackup: "/v1/containers/%s/backup", // 容器备份
    entryPoints: "/v1/containers/entrypoints", // 获取所有服务入口点
    ContainerWithNet: "/v1/containers/%s/network", // 根据容器名获取指定网络
    delContainerNet: "/v1/networks/%s/disconnect", // 根据容器名称删除网络
    addContainerRouter: "/v1/containers/%s/route", // 设置容器默认路由 | 获取容器路由
    /* 镜像 */
    images: "/v1/images/json", // 获取镜像
    imageDel: "/v1/images/%s", // 删除镜像
    imageInspect: "/v1/images/%s/json", // 镜像信息
    imageTag: "/v1/images/%s/tag", // 新建tag
    /* 网络 */
    networkCreate: "/v1/networks/create", // 创建网络
    networkDel: "/v1/networks/%s", // 删除网络
    networks: "/v1/networks", // 获取网络
    netConnectContainer: "/v1/networks/%s/connect", // 容器连接网络
    networkInspect: "/v1/networks/%s",
    /* 存储 */
    storageCreate: "/v1/volumes/create", // 创建存储卷
    storages: "/v1/volumes", // 挂载卷
    storageInspect: "/v1/volumes/%s", // 挂载卷信息
    /* 用户信息 */
    user: "/user/%s/info", // 获取用户信息
    users: "/user/info", // 获取所有用户信息
    userRegister: "/user/register", // 用户注册
    userDel: "/user/delete_user", // 删除用户
    updateUserInfo: "/user/change_password", // 修改用户密码
    updateRole: "/user/change_role", // 修改用户角色
    updateDes: "/user/change_comment", // 修改用户描述
    /* 更换图片 */
    uploadLogo: "/v1/platform/uploadLogo",
    /* 系统 */
    systemInfo: "/system/info", // 获取宿主机状态信息
    setSystemNet: "/system/change_system_ip", // 修改宿主机Ip
    containerLogs: "/v1/containers/%s/service/logs", // 下载容器日志
    networkCards: "/v1/networks/interfaces", // 获取物理网卡信息
    systemRestart: "/system/reboot", // 宿主机重启
    systemShutdown: "/system/shutdown", // 宿主机关机
    /* 堆栈 */
    stackCreate: "/v1/stack/up", // 创建并启动堆栈
    stackStart: "/v1/stack/start", // 启动堆栈
    stackRestart: "/v1/stack/restart", // 重启堆栈
    stackStop: "/v1/stack/stop", // 停止堆栈
    stackRemove: "/v1/stack/down", // 移除堆栈
    stackConfig: "/v1/stack/config", // 获取堆栈
    stackJson: "/v1/stack/json", // 获取堆栈
  },
  /* 系统图片 */
  pictures: {
    corpLogoMini: {
      url: "/imgs/corpLogoMini.png",
      type: "png",
      property: "corpLogoMini",
      size: "60 x 40",
    },
    coryLogo: {
      url: "/imgs/corpLogo.png",
      type: "png",
      property: "corpLogo",
      size: "200 x 40",
    },
    favicon: {
      url: "/imgs/favicon.ico",
      type: "ico",
      property: "favicon",
      size: "19 x 19",
    },
    loginBackground: {
      url: "/imgs/loginBg.png",
      type: "png",
      property: "loginBg",
      size: "1920 x 1080",
    },
    loginLogo: {
      url: "/imgs/loginLogo.png",
      type: "png",
      property: "loginLogo",
      size: "322 x 24",
    },
  },
};

export default config;

export const SERVICE_PLATFORM = config;
