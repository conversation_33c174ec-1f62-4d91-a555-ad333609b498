import { createApp } from 'vue';
import 'element-plus/dist/index.css';
import './style/index.scss'; // 全局样式
import formCreate from '@form-create/element-ui';
import axios from 'axios';
import install from '@form-create/element-ui/auto-import';
import NProgress from 'nprogress'; // 进度条
import JsonViewer from 'vue3-json-viewer'; // json数据预览
import 'vue3-json-viewer/dist/index.css';
import Codemirror from "vue-codemirror";
import SERVICE_PLATFORM from '@/core/config';
import run from './core/service-platform'; // 全局定义
import App from './App';
import router from './router/index';
import 'nprogress/nprogress.css'; // 进度条样式
import session from './util/session';
import { refreshToken } from './util/doRequest';

window.tokenRefreshing = false; // 正在刷新令牌
let cacheRequestArr = []; // 缓存请求

// 将所有的请求都push到数组中
function cacheRequestArrHandle(cb) {
  cacheRequestArr.push(cb);
}
// 用新的token去重新发起请求
function afreshRequest(token) {
  cacheRequestArr.map((cb) => cb(token));
  cacheRequestArr = [];
}
// 判断token是否即将过期
function isTokenExpired() {
  const curTime = new Date().getTime();
  const expiresTime = Number(session.getExpire()) - curTime;
  const minutesTime = new Date(expiresTime).getMinutes();
  if (expiresTime >= 0 && minutesTime < 10) {
    console.log('expiresTime:', expiresTime, ' minutesTime:', minutesTime);
    return true;
  }
  return false;
}

const service = axios.create({
  baseURL: SERVICE_PLATFORM.serverBaseURL,
  timeout: 15000,
});

/* 请求拦截器 */
service.interceptors.request.use((config) => {
  if (config.url !== SERVICE_PLATFORM.serverApi.systemInfo) {
    NProgress.start(); // 显示进度条
  }
  const isLogin = session.getToken();
  if (isLogin) {
    // 为请求头对象，添加 Token 字段
    config.headers.Authorization = `Bearer ${isLogin}`;
  }

  // 判断token是否即将过期，且不是请求刷新token的接口
  if (isTokenExpired() && config.url !== SERVICE_PLATFORM.serverApi.refreshToken) {
    if (!window.tokenRefreshing) {
      window.tokenRefreshing = true;
      refreshToken(service, (res) => {
        session.setTokenAndExpire(res.data.token, res.data.expire);
        afreshRequest(session.getToken());
      });
      window.isRefreshing = false;

      const retry = new Promise((resolve) => {
        cacheRequestArrHandle((token) => {
          config.headers.Authorization = `Bearer ${token}`; // token为刷新完成后传入的token
          // 将请求挂起
          resolve(config);
        });
      });
      return retry;
    }
  }
  return config;
});

/* 响应拦截器 */
service.interceptors.response.use(
  (response) => {
    NProgress.done(); // 清除进度条
    return response;
  },
  ((error) => {
    NProgress.done(); // 清除进度条
    const errorCode = error.response.data.code;
    const errorUrl = error.response.config.url;
    if (errorCode === 401 && errorUrl !== SERVICE_PLATFORM.serverApi.loginServer) {
      const url = `${SERVICE_PLATFORM.webBaseURL}/login`;
      ElMessage.error('会话已失效，请重新登录');
      session.removeToken();
      window.location.replace(url); // 重定向到登录页面，并且不允许后退或前进
    }
    return Promise.reject(error);
  }),
);

const app = createApp(App);
app.config.globalProperties.$http = service;

formCreate.use(install);
app.use(JsonViewer)
  .use(router)
  .use(run)
  .use(Codemirror)
  .use(formCreate);
app.mount('#app');
