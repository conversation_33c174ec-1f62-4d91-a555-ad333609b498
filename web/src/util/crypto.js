import CryptoJS from 'crypto-js';

const defaultKey = CryptoJS.enc.Utf8.parse('cteQeL7mZE2HUgIHbrVB35Fx2gRhNUH4');

/**
 * AES 对称加密
 */
export default {

  /**
   * 接口数据加密函数
   * @param str string 需加密字符串
   * @param key string 加密key(16位)
   * @return string 加密密文字符串
   */
  encrypt(str, key) {
    if (!key) key = defaultKey;
    const encrypted = CryptoJS.AES.encrypt(str, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
  },

  /**
   * 接口数据解密函数
   * @param str string 已加密密文
   * @param key string 加密key(16位)
   * @returns {*|string} 解密之后的json字符串
   */
  decrypt(str, key) {
    if (!key) key = defaultKey;
    const decrypt = CryptoJS.AES.decrypt(str, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
  },
};
