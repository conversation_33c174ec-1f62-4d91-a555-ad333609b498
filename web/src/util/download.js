export default {
  /**
   * 下载压缩文件.
   * @param content response.
   * @param fileName 文件名.
   * @param suffixName 文件格式后缀.
   */
  downloadTar(content, fileName, suffixName) {
    const blob = new Blob([content], {
      type: 'application/x-tar',
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.${suffixName}`;
    a.click();
  },
};
