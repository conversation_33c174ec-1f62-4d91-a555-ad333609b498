/* eslint-disable */
/**
 * Ip地址格式检查.
 * @param ipv4 Ip地址
 * @returns {boolean}
 */
export const isIpAddr = (ipv4) => {
  let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  return reg.test(ipv4);
}

/**
 * 判断两个Ip是否属于同一子网.
 * @param addr1 Ip地址.
 * @param addr2 Ip地址.
 * @param mask CDIR.
 * @returns {boolean} 若属于同一子网返回true.
 */
export const isEqualIpAddress =  (addr1, addr2, mask) => {
  if(!addr1 || !addr2 || !mask){
    console.log("参数不能为空");
    return false;
  }
  let netmask = CDIR2netmask(mask);
  let
    res1 = [],
    res2 = [];
  addr1 = addr1.split(".");
  addr2 = addr2.split(".");
  mask  = netmask.split(".");
  for(let i = 0; i < addr1.length ; i += 1){
    res1.push(parseInt(addr1[i]) & parseInt(mask[i]));
    res2.push(parseInt(addr2[i]) & parseInt(mask[i]));
  }
  if(res1.join(".") === res2.join(".")){
    return true;
  }else{
    return false;
  }

}

/**
 * 读取文件内容.
 * @param file .
 * @returns {Promise<unknown>}
 */
export const readerFile = (file) => {
  const reader = new FileReader()
  const promise = new Promise((resolve, reject) => {
    reader.onload = function () {
      resolve(reader.result)
    }
    reader.onerror = function (e) {
      reader.abort()
      reject(e)
    }
  })
  reader.readAsText(file, 'UTF-8') // 将文件读取为文本
  return promise
};

/**
 * 如：24 转 255.255.255.0
 * @param bitCount 有效位数.
 * @returns {string} 掩码.
 * @constructor .
 */
function CDIR2netmask (bitCount) {
  let mask = [];
  for (let i = 0; i < 4; i++) {
    let n = Math.min(bitCount, 8);
    mask.push(256 - Math.pow(2, 8 - n));
    bitCount -= n;
  }
  return mask.join('.');
}

/**
 * 根据文件名判断是否为yaml文件.
 * @param {string} fileName 文件名.
 * @returns {boolean} .
 */
export const isYaml = (fileName) => {
  if (fileName) {
    return fileName.substring(fileName.lastIndexOf('.') + 1) === 'yaml';
  }
  return false;
}
