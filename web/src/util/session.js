import crypto from './crypto';

const tokenKey = 'token';
const expireTimeKey = 'expire';

export default {
  /**
   * 获取令牌.
   * @returns {string} .
   */
  getToken() {
    return window.sessionStorage.getItem(tokenKey);
  },

  /**
   * 获取令牌过期时间.
   * @returns {number} .
   */
  getExpire() {
    return new Date(window.sessionStorage.getItem(expireTimeKey)).getTime();
  },

  /**
   * 缓存令牌及过期时间.
   * @param token 令牌.
   * @param expire 过期时间.
   */
  setTokenAndExpire(token, expire) {
    window.sessionStorage.setItem(tokenKey, token);
    window.sessionStorage.setItem(expireTimeKey, expire);
  },

  /**
   * 清除缓存的令牌信息.
   */
  removeToken() {
    return window.sessionStorage.clear();
  },

  /**
   * 缓存用户信息.
   * @param userInfo {string} .
   */
  setUserInfo(userInfo) {
    window.sessionStorage.setItem(crypto.encrypt('user'), crypto.encrypt(userInfo));
  },

  /**
   * 获取用户信息.
   * @returns {string}
   */
  getUserInfo() {
    return crypto.decrypt(window.sessionStorage.getItem(crypto.encrypt('user')));
  },
};
