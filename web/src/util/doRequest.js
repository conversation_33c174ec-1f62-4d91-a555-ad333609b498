import config from '../core/config';
import { formatDate } from './transferFormat';
import crypto from './crypto';

/**
 * 获取软件信息.
 * @param http axios.
 * @param callback
 * @returns {Promise<void>}
 */
export const getSoftInfo = async function (http, callback) {
  const url = config.serverApi.about;
  await http.get(url).then((res) => {
    callback(res);
  }).catch((error) => {
    callback(error);
  });
};
/**
 * 根据容器名读取网络信息.
 *
 * @param http axios.
 * @param containerName 容器名称.
 * @param callback 回调.
 */
export const getContainerWithNet = async function (http, containerName, callback) {
  const url = config.serverApi.ContainerWithNet.replace('%s', containerName);
  await http.get(url).then((res) => {
    callback(res);
  }).catch((error) => {
    callback(error);
  });
};

/**
 * 获取所有镜像.
 * @param http axios.
 * @param callback
 * @returns {Promise<void>}
 */
export const getImages = async (http, callback) => {
  const url = config.serverApi.images;
  const imageData = [];
  await http.get(url).then((res) => {
    res.data.forEach((image) => {
      imageData.push({
        id: image.Id,
        status: '',
        tag: image.RepoTags,
        size: Math.floor(image.Size / 1024 / 1024),
        created: formatDate(image.Created * 1000),
      });
    });
    callback(imageData, null);
  }).catch((error) => {
    callback(null, error);
  });
};

/**
 * 获取所有可以加入的网络.
 * @param http
 * @param callback
 * @returns {Promise<void>}
 */
export const getNetworks = async (http, callback) => {
  const url = config.serverApi.networks;
  await http.get(url).then((res) => {
    const result = [];
    let subnets;
    let gateways;
    res.data.forEach((body) => {
      subnets = [];
      gateways = [];
      body.config.forEach((net) => {
        subnets.push(net.subnet);
        gateways.push(net.gateway);
      });
      result.push({
        driver: body.driver,
        id: body.id,
        name: body.name,
        parent: body.parent,
        subnet: subnets.toString(),
        gateway: gateways.toString(),
      });
    });
    callback(result);
  }).catch((error) => {
    callback(error);
  });
};

/**
 * 获取存储卷.
 * @param http
 * @param callback
 * @returns {Promise<void>}
 */
export const getVolumes = async (http, callback) => {
  const url = config.serverApi.storages;
  const volumeData = [];
  await http.get(url).then((res) => {
    res.data.Volumes.forEach((volume) => {
      volumeData.push({
        name: volume.Name,
        scope: volume.Scope,
        driver: volume.Driver,
        created: formatDate(volume.CreatedAt),
        mountPoint: volume.Mountpoint,
        labels: volume.Labels,
      });
    });
    callback(volumeData);
  }).catch((error) => {
    callback(error);
  });
};

/**
 * 刷新令牌.
 * @param http
 * @param callback
 */
export const refreshToken = (http, callback) => {
  const url = config.serverApi.refreshToken;
  http.get(url).then((res) => {
    callback(res);
  }).catch((error) => {
    callback(error);
  });
};

/**
 * 获取指定用户的信息.
 * @param http
 * @param username 用户名.
 * @param callback
 */
export const getUserInfo = (http, username, callback) => {
  const url = config.serverApi.user.replace('%s', username);
  const userInfo = {};
  http.get(url).then((res) => {
    userInfo.username = res.data.Username;
    userInfo.role = res.data.Role;
    callback(userInfo);
  }).catch((error) => {
    callback(error);
  });
};

/**
 * 读取系统状态.
 * @param http
 * @param callback
 */
export const getSystemInfo = async (http, callback) => {
  const url = config.serverApi.systemInfo;
  http.get(url).then((res) => {
    callback(res.data, null);
  }).catch((error) => {
    callback(null, error);
  });
};
