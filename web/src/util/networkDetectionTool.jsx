import { ElButton, ElForm, ElFormItem, ElInput, ElMessageBox } from "element-plus";
import { reactive, ref } from "vue";
import { SERVICE_PLATFORM } from "../core/config";

function createWebsocket({ url, onopen, onclose, onmessage }) {
  let $ws = null;
  const init = (_onopen = onopen) => {
    const ws = new WebSocket(url);
    ws.onopen = _onopen;
    ws.onclose = (...args) => {
      onclose?.(...args);
      $ws = null;
    };
    ws.onmessage = onmessage;
    return ws;
  };

  return {
    getInstance() {
      return $ws;
    },
    open() {
      if ($ws) return;
      $ws = init();
    },
    close() {
      $ws?.close();
    },
    send(data) {
      if (!$ws) {
        $ws = init(() => {
          onopen();
          $ws.send(data);
        });
      } else {
        $ws.send(data);
      }
    },
  };
}

export const networkDetectionTool = () => {
  return {
    showDialog() {
      const panelRef = ref();
      const formRef = ref();
      const pinging = ref(false);
      const data = reactive({
        ip: "",
      });
      const ws = createWebsocket({
        url: `${SERVICE_PLATFORM.websocketBaseURL}/gping`,
        onopen: () => {},
        onclose: () => {},
        onmessage: (event) => {
          panelRef.value.innerHTML += `${event.data}<br/>`;
          panelRef.value.scrollTop = panelRef.value.scrollHeight;
        },
      });

      const handleSend = () => {
        formRef.value.validate((validate) => {
          if (!validate) return;
          panelRef.value.innerHTML = "";
          pinging.value = true;
          ws.send(data.ip);
        });
      };

      const handleStop = () => {
        pinging.value = false;
        ws.send("stop");
      };

      const rules = {
        ip: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error("地址不能为空"));
              } else if (!/^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(:\d+)?$/.test(value)) {
                callback(new Error("IP地址格式不正确"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      };

      ElMessageBox({
        title: "网络检测工具",
        message: () => (
          <div style="width: 600px;margin:auto;">
            <ElForm inline hide-required-asterisk ref={formRef} rules={rules} model={data}>
              <ElFormItem style="width:400px;" prop="ip">
                <ElInput v-model={data.ip} placeholder="请输入要检测的IP地址（支持携带端口号）" />
              </ElFormItem>
            </ElForm>
            <div style="margin-bottom:1em">
              <ElButton type="primary" disabled={pinging.value} onclick={handleSend}>
                PING
              </ElButton>
              <ElButton type="danger" disabled={!pinging.value} onclick={handleStop}>
                STOP
              </ElButton>
            </div>
            <div
              style="width:100%;height:240px;padding:10px; border:1px solid var(--el-border-color); overflow:hidden auto;box-sizing: border-box;border-radius: var(--el-border-radius-base);"
              ref={panelRef}
            ></div>
          </div>
        ),

        showClose: false,
        showCancelButton: true,
        cancelButtonText: "关闭",
        showConfirmButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customStyle: {
          "--el-messagebox-width": "640px",
        },
      }).catch(() => {
        handleStop();
        ws.close();
      });
    },
  };
};
