/* eslint-disable */
/**
 * 将2022-05-23T09:58:50+08:00格式转换为2022-05-23 09:58:50.
 * @param time 2022-05-23T09:58:50+08:00.
 * @returns {string} .
 */
export const formatDate = (time) => {
  if (time == null || time == '') return '';
  const date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
  const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
  const hours = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
  const minutes = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};


import YAML from 'yaml'
import yaml from 'js-yaml'

// json转换为yaml格式
export const json2yaml = (jsonData) => {
  try {
    return {
      data: typeof (jsonData) === 'string' ? yaml.dump(JSON.parse(jsonData)) : yaml.dump(jsonData),
      error: false
    }
  } catch (err) {
    return {
      data: '',
      error: true
    }
  }
}


// yamlStr 为字符串形式的yaml数据
// returnString 是否返回字符串格式的json数据
export const yaml2json = (yamlStr, returnString) => {
  try {
    return {
      data: returnString ? JSON.stringify(YAML.parse(yamlStr), null, 2) : YAML.parse(yamlStr),
      error: false
    }
  } catch (err) {
    return {
      data: '',
      error: true
    }
  }
}

