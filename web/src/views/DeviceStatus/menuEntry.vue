<template>
  <div class="app-container page">
    <div class="filter-container">
      <el-card class="gva-card">
        <template #header>
          <div class="card-header">
            <b>菜单入口</b>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col
              v-for="(card, key) in toolCards"
              :key="key"
              :span="4"
              :xs="8"
              class="quick-entrance-items"
              @click="toTarget(card.name)"
          >
            <div class="quick-entrance-item">
              <div class="quick-entrance-item-icon" :style="{ backgroundColor: card.bg }">
                <el-icon>
                  <component :is="card.icon" :style="{ color: card.color }" />
                </el-icon>
              </div>
              <p>{{ card.label }}</p>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="gva-card">
        <template #header>
          <div class="card-header">
            <b>更换图片</b>
          </div>
        </template>
        <el-row :gutter="20">
          <!-- 系统图片  -->
          <el-table :data="systemImageList" height="300" style="width: 100%" border stripe>
            <el-table-column fixed type="index" />
            <el-table-column property="name" label="图片" sortable >
              <template v-slot="scope">
                <el-image
                    style="width: 70%; height: 80px"
                    :src="`${this.$SERVICE_PLATFORM.serverBaseURL}${scope.row.url}`"
                >
                </el-image>
              </template>
            </el-table-column>
            <el-table-column property="type" label="类型" sortable />
            <el-table-column property="size" label="大小(像素)" sortable />
            <el-table-column property="property" label="属性" sortable />
            <el-table-column label="操作" width="180px">
              <template v-slot="scope">
                <!-- 上传按钮 -->
                <el-upload
                    class="upload-demo"
                    accept=".png, .ico"
                    :action="`${upload.url}?type=${scope.row.property}`"
                    :headers="upload.headers"
                    multiple
                    :limit="1"
                >
                  <el-tooltip effect="dark" content="上传替换" placement="top" :enterable="false">
                    <el-button type="primary" size="small"><el-icon><CopyDocument /></el-icon></el-button>
                  </el-tooltip>
                </el-upload>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      toolCards: [
        {
          label: '容器管理',
          icon: 'monitor',
          name: 'container',
          color: '#ff9c6e',
          bg: 'rgba(255, 156, 110,.3)',
        },
        {
          label: '镜像管理',
          icon: 'compass',
          name: 'image',
          color: '#69c0ff',
          bg: 'rgba(105, 192, 255,.3)',
        },
        {
          label: '网络管理',
          icon: 'connection',
          name: 'network',
          color: '#b37feb',
          bg: 'rgba(179, 127, 235,.3)',
        },
      ],
      systemImageList: [],
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: `Bearer ${window.sessionStorage.getItem('token')}` },
        // 上传的地址
        url: `${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.serverApi.uploadLogo}`,
      },
    };
  },
  created() {
    this.getImages();
  },
  methods: {
    async toTarget(name) {
      console.log('跳转', name);
      await this.$router.push(`/${name}`);
    },

    async getImages() {
      Object.keys(this.$SERVICE_PLATFORM.pictures).forEach((key) => {
        this.systemImageList.push(this.$SERVICE_PLATFORM.pictures[key]);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
}
.page {
  .gva-card {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 2px;
    height: auto;
    padding: 26px 30px;
    overflow: hidden;
    box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.03);
  }
  .card-header{
    padding-bottom: 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .quick-entrance-items {
    @include flex-center;
    justify-content: center;
    text-align: center;
    color: #333;
    .quick-entrance-item {
      padding: 16px 28px;
      margin-top: -16px;
      margin-bottom: -16px;
      border-radius: 4px;
      transition: all 0.2s;
      &:hover{
        box-shadow: 0px 0px 7px 0px rgba(217, 217, 217, 0.55);
      }
      cursor: pointer;
      height: auto;
      text-align: center;
      // align-items: center;
      &-icon {
        width: 50px;
        height: 50px !important;
        border-radius: 8px;
        @include flex-center;
        justify-content: center;
        margin: 0 auto;
        i {
          font-size: 24px;
        }
      }
      p {
        margin-top: 10px;
      }
    }
  }
}
</style>
