<template>
  <div class="app-container">
    <div class="filter-container">
      <!-- 卡片视图区域 -->
      <el-card>
        <template #header>
          <div class="header">
            <el-button type="primary" @click="createVolumeVis = true">创建</el-button>
            <el-button type="danger" @click="deleteVolume">删除</el-button>
            <el-button type="primary" @click="getStorageList">刷新数据</el-button>
          </div>
        </template>
        <!-- 存储卷列表区域 -->
        <el-table :data="storageList" height="600" style="width: 100%" border stripe
                  @selection-change="selectionChange">
          <el-table-column fixed type="index" />
          <el-table-column fixed type="selection" width="50" />
          <el-table-column label="名称" prop="name" sortable></el-table-column>
          <el-table-column label="级别" prop="scope" sortable></el-table-column>
          <el-table-column label="驱动" prop="driver" sortable></el-table-column>
          <el-table-column label="挂载点" prop="mountPoint" sortable></el-table-column>
          <el-table-column label="创建时间" prop="created" sortable></el-table-column>
          <el-table-column label="操作" width="180px">
            <template v-slot="scope">
              <el-row :gutter="5">
                <el-space wrap>
                  <el-col :span="2">
                    <!-- 详情按钮 -->
                    <el-tooltip effect="dark" content="详情" placement="top" :enterable="false">
                      <el-button type="primary" size="small" @click="handleInfo(scope.row)"><el-icon><Postcard /></el-icon></el-button>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="2">
                    <el-popconfirm title="此操作将永久删除, 是否继续?" @confirm="handleDel(scope.row.name)">
                      <template #reference>
                        <div>
                          <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                            <el-button type="danger" size="small"><el-icon><delete /></el-icon></el-button>
                          </el-tooltip>
                        </div>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-space>
              </el-row>

            </template>
          </el-table-column>
        </el-table>

        <el-dialog
            v-model="createVolumeVis"
            title="创建存储卷"
            width="30%"
            draggable
        >
          <form-create :rule="createVolumeRule" :option="resetOn" @submit="createVolumeSubmit"></form-create>
        </el-dialog>

        <el-dialog
            v-model="volumeInspectVis"
            title="挂载卷信息"
            width="50%"
            draggable
        >
          <json-viewer :value="volumeInfo" copyable boxed sort />
        </el-dialog>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getVolumes } from '../../util/doRequest';

export default {
  name: 'storageManage',
  data() {
    return {
      volume: '',
      volumeInfo: '',
      volumeInspectVis: false,
      createVolumeVis: false, // 创建存储卷的表单显示
      selection: '', // 表格选中项
      storageList: [],
      // 创建存储卷的表单规则
      createVolumeRule: [
        {
          type: 'input',
          field: 'Name',
          title: '名称',
          // 验证规则
          validate: [
            {
              required: true,
              message: '请输入存储卷名',
              trigger: 'blur',
            },
            {
              min: 1,
              max: 20,
              message: '长度在 1 - 20个字符之间',
              trigger: 'blur',
            },
            {
              pattern: /^\w+$/,
              message: '仅支持英文数字下划线输入',
            },
          ],
        },
      ],
      // 显示重置按钮
      resetOn: {
        resetBtn: true,
      },
    };
  },
  created() {
    this.getStorageList();
  },
  methods: {
    /* 获取挂在卷 */
    getStorageList() {
      getVolumes(this.$http, (res, error) => {
        if (res) {
          this.storageList = res;
        } else {
          console.error(error);
        }
      });
    },
    /**
     * 创建存储卷.
     * @param formData 表单数据.
     */
    createVolumeSubmit(formData) {
      const url = this.$SERVICE_PLATFORM.serverApi.storageCreate;
      this.$http.post(url, JSON.stringify(formData)).then(() => {
        ElMessage.success('创建成功');
        this.getStorageList();
        this.createVolumeVis = false;
      }).catch((error) => {
        ElMessage.error('创建失败');
        console.log('创建失败', error);
      });
    },
    /**
     * 删除单个或多个挂载卷.
     * @param name
     */
    deleteVolume() {
      if (this.selection.length <= 0) {
        ElMessage.info('请先选中挂载卷');
        return;
      }
      ElMessageBox.confirm('此操作将永久删除该存储卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.selection.forEach((select) => {
          const url = `${this.$SERVICE_PLATFORM.serverApi.storages}/${select.name}`;
          this.$http.delete(url).then(() => {
            ElMessage.success('删除成功');
            this.getStorageList();
          }).catch((error) => {
            ElMessage.error('删除失败');
            console.log('删除失败', error);
          });
        });
      }).catch(() => {
        ElMessage.info('取消删除');
      });
    },
    /**
     * 表格的选中事件.
     * @param val .
     */
    selectionChange(val) {
      this.selection = val;
    },

    handleInfo(row) {
      this.volume = row;
      this.volumeInspectVis = true;
      const url = this.$SERVICE_PLATFORM.serverApi.storages.replace('%s', row.name);
      this.$http.get(url).then((res) => {
        this.volumeInfo = res.data;
      }).catch((error) => {
        console.error(error);
      });
    },
    /**
     * 操作单个删除的按钮事件.
     * @param name .
     */
    handleDel(name) {
      const url = `${this.$SERVICE_PLATFORM.serverApi.storages}/${name}`;
      this.$http.delete(url).then(() => {
        ElMessage.success('删除成功');
        this.getStorageList();
      }).catch((error) => {
        ElMessage.error('删除失败');
        console.error('删除失败', error);
      });
    },
  },
};
</script>
