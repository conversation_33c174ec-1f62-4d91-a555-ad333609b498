<template>
  <div>
    <div class="app-container">
      <div class="filter-container">
        <!-- 卡片视图区域 -->
        <el-card>
          <template #header>
            <div class="header">
              <el-button type="primary" @click="createNetworkVis = true">创建</el-button>
              <el-button type="danger" @click="handleDelNetwork">删除</el-button>
              <el-button type="primary" @click="getNetworkList">刷新数据</el-button>
            </div>
          </template>
          <!-- 镜像列表区域  -->
          <el-table :data="networkList" height="600" style="width: 100%" border stripe
                    @selection-change="selectionChange">
            <el-table-column fixed type="index" />
            <el-table-column fixed type="selection" width="50" />
            <el-table-column property="name" label="网络名称" sortable />
            <el-table-column property="driver" label="驱动" sortable />
            <el-table-column property="subnet" label="子网" sortable />
            <el-table-column property="gateway" label="网关" sortable />
            <el-table-column property="parent" label="父网卡" sortable />
            <el-table-column label="操作" width="180px">
              <template v-slot="scope">
                <el-row :gutter="5">
                  <el-space wrap>
                    <el-col :span="2">
                      <!-- 详情按钮 -->
                      <el-tooltip effect="dark" content="详情" placement="top" :enterable="false">
                        <el-button type="primary" size="small" @click="handleInfo(scope.row)"><el-icon><Postcard /></el-icon></el-button>
                      </el-tooltip>
                    </el-col>
                    <!-- 删除按钮 -->
                    <el-col :span="2">
                      <el-popconfirm title="此操作将永久删除, 是否继续?" @confirm="handleDel(scope.row.name)">
                        <template #reference>
                          <div>
                            <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                              <el-button type="danger" size="small"><el-icon><delete /></el-icon></el-button>
                            </el-tooltip>
                          </div>
                        </template>
                      </el-popconfirm>
                    </el-col>
                  </el-space>
                </el-row>

              </template>
            </el-table-column>
          </el-table>

          <el-dialog
              v-model="createNetworkVis"
              title="创建网络"
              width="35%"
              draggable
          >
            <el-form
                ref="networkFormRef"
                :model="createNetworkForm"
                :rules="createNetworkRule"
                label-width="95px"
            >
              <el-form-item label="名称" prop="name">
                <el-input v-model="createNetworkForm.name" placeholder="请输入网络名称"></el-input>
              </el-form-item>
              <el-form-item label="驱动" prop="driver">
                <el-select v-model="createNetworkForm.driver">
                  <el-option
                      v-for="item in ['ipvlan', 'macvlan']"
                      :key="item"
                      :label="item"
                      :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="父网卡" prop="parent">
                <el-select v-model="createNetworkForm.parent">
                  <el-option
                      v-for="(item, index) in networkCardList"
                      :key="index"
                      :label="item.name"
                      :value="item.name"
                  >
                    <span style="float: right">
                     <el-icon color="green" v-if="item.connected"><Connection /></el-icon>
                     <el-icon v-else><RemoveFilled /></el-icon>
                    </span>
                    <span style="padding-left: 6px;">{{ item.name }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="查重" prop="checkDuplicate">
                <el-switch v-model="createNetworkForm.checkDuplicate"></el-switch>
              </el-form-item>
              <el-form-item label="子网&网关" v-for="(net, index) in createNetworkForm.config" :key="index">
                <el-col :span="10">
                  <el-form-item
                      :prop="`config[${index}].subnet`"
                      :rules="createNetworkRule.subnet"
                  >
                    <el-input v-model="net.subnet" placeholder="请输入子网地址"></el-input>
                  </el-form-item>
                </el-col>
                <el-icon><Link /></el-icon>
                <el-col :span="10">
                  <el-form-item
                      :prop="`config[${index}].gateway`"
                      :rules="createNetworkRule.gateway"
                  >
                    <el-input v-model="net.gateway" placeholder="请输入网关地址"></el-input>
                  </el-form-item>
                </el-col>
                <el-button size="default" @click="addSubnet" style="width: 35px" link><el-icon><plus /></el-icon></el-button>
                <el-button size="small" link @click="cancelSubnet(net)"><el-icon><delete /></el-icon></el-button>

              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="createNetworkSubmit">提交</el-button>
                <el-button @click="createNetworkVis = false">取消</el-button>
              </span>
            </template>
          </el-dialog>

          <el-dialog
              v-model="networkInfoVis"
              title="网络信息"
              width="50%"
              draggable
          >
            <json-viewer :value="networkInfo" copyable boxed sort />
          </el-dialog>
        </el-card>
      </div>
    </div>
  </div>

</template>

<script>
import { getNetworks, getSystemInfo } from '../../util/doRequest';

export default {
  name: 'networkManage',
  data() {
    return {
      createNetworkVis: false, // 创建网络的表单显示
      networkInfoVis: false, // 网络inspect信息显示
      networkInfo: '',
      network: '', // 点击操作中的按钮的对应的网络信息
      selection: '', // 表格选中项
      networkList: [], // 所有网络信息
      networkCardList: [], // 父网卡名
      createNetworkForm: {
        name: '', // 网络名称
        driver: 'ipvlan', // 驱动方式
        parent: '', // 父网卡
        checkDuplicate: true, // 查重
        config: [ // 子网
          {
            seq: 0,
            subnet: '',
            gateway: '',
          },
        ],
      },
      // 创建网络的表单规则
      createNetworkRule: {
        name: [
          {
            required: true,
            message: '请输入网络名',
            trigger: 'blur',
          },
          {
            pattern: /^\w+$/,
            message: '仅支持英文数字下划线输入',
          },
        ],
        subnet: [
          {
            required: true,
            message: '请输入子网地址',
            trigger: true,
          },
          {
            pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\/(\d{1,2})$/,
            message: '请输入正确的地址格式,如 ************/24',
          },
        ],
        gateway: [
          {
            required: true,
            message: '请输入网关地址',
            trigger: true,
          },
          {
            pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
            message: '请输入正确的网关格式',
          },
        ],
        parent: [
          {
            required: true,
            message: '请选择父网卡',
            trigger: true,
          },
        ],
      },
      // 显示重置按钮
      resetOn: {
        resetBtn: true,
      },
    };
  },
  created() {
    getSystemInfo(this.$http, (data, error) => {
      if (!error) {
        data.network.forEach((net) => {
          this.networkCardList.push({
            name: net.name,
            connected: net.connected,
          });
        });
      }
    });
    this.getNetworkList();
  },
  methods: {
    getNetworkList() {
      getNetworks(this.$http, (data, error) => {
        if (data) {
          this.networkList = data;
        } else if (error) {
          console.error('get Networks error', error);
        }
      });
    },

    /**
     * 表格的选中事件.
     * @param val .
     */
    selectionChange(val) {
      this.selection = val;
    },

    /**
     * 删除按钮点击事件处理.
     */
    handleDelNetwork() {
      if (this.selection.length <= 0) {
        ElMessage.info('请先选中网卡');
        return;
      }
      ElMessageBox.confirm('此操作将永久删除该网卡, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.selection.forEach((select) => {
          const url = this.$SERVICE_PLATFORM.serverApi.networkDel.replace('%s', select.name);
          this.$http.delete(url).then(() => {
            ElMessage.success('删除成功');
            this.getNetworkList();
          }).catch((error) => {
            ElMessage.error('删除失败');
            console.log('强制删除失败', error);
          });
        });
      }).catch(() => {
        ElMessage.info('取消删除');
      });
    },
    /**
     * inspect信息.
     * @param row
     */
    handleInfo(row) {
      this.network = row;
      this.networkInfoVis = true;
      const url = this.$SERVICE_PLATFORM.serverApi.networkInspect.replace('%s', row.id);
      this.$http.get(url).then((res) => {
        this.networkInfo = res.data;
      }).catch((error) => {
        ElMessage.error('获取网络信息失败');
        console.error(error);
      });
    },

    /**
     * 操作单个删除的按钮事件.
     * @param name .
     */
    handleDel(name) {
      const url = this.$SERVICE_PLATFORM.serverApi.networkDel.replace('%s', name);
      this.$http.delete(url).then(() => {
        ElMessage.success('删除成功');
        this.getNetworkList();
      }).catch((error) => {
        ElMessage.error('删除失败');
        console.error('删除失败', error);
      });
    },

    /**
     * 提交创建网卡表单.
     */
    async createNetworkSubmit() {
      await this.$refs.networkFormRef.validate((valid, fields) => {
        if (valid) {
          this.$http.post(this.$SERVICE_PLATFORM.serverApi.networkCreate, this.createNetworkForm).then(() => {
            ElMessage.success('创建成功');
            this.getNetworkList();
          }).catch((error) => {
            ElMessage.error('创建网卡失败');
            console.error('创建网卡失败', error);
          });
          this.createNetworkVis = false;
        } else {
          console.log('error submit!');
        }
      });
    },
    /**
     * 增加子网输入项.
     */
    addSubnet() {
      let len = this.createNetworkForm.config.length;
      len += 1;
      this.createNetworkForm.config.push({
        seq: len,
        net: '',
        gateway: '',
      });
    },
    /**
     * 删除子网输入项.
     */
    cancelSubnet(subnet) {
      if (this.createNetworkForm.config.length > 1) {
        for (let i = 0; i < this.createNetworkForm.config.length; i += 1) {
          if (this.createNetworkForm.config[i].seq === subnet.seq) {
            this.createNetworkForm.config.splice(i, 1);
            return;
          }
        }
      }
    },
  },
};
</script>

<style scoped>

</style>
