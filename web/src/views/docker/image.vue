<template>
  <div>
    <div class="app-container">
      <div class="filter-container">
        <!-- 卡片视图区域 -->
        <el-card>
          <template #header>
            <div class="header">
              <el-button type="primary" @click="uploadImageVis = true">上传镜像</el-button>
              <el-button type="danger" @click="delImage">删除</el-button>
              <el-button type="primary" @click="getImageList">刷新数据</el-button>
            </div>
          </template>
          <!-- 镜像列表区域  -->
          <el-table :data="imageList" height="600" style="width: 100%" border stripe
                    @selection-change="selectionChange"
                    v-loading="loadingVis"
                    element-loading-background="rgba(255,255,255,0.7)"
                    element-loading-text="文件上传中......"
          >
            <el-table-column fixed type="index" />
            <el-table-column fixed type="selection" width="50" />
            <el-table-column property="id" label="ID" sortable />
            <el-table-column property="status" label="状态" background-color="#ccc" sortable />
            <el-table-column property="tag" label="标签" sortable />
            <el-table-column property="size" label="大小(MB)" sortable />
            <el-table-column property="created" label="创建时间" sortable />
            <el-table-column label="操作" width="180px">
              <template v-slot="scope">
                <el-row :gutter="5">
                  <el-space wrap>
                    <el-col :span="2">
                      <!-- 详情按钮 -->
                      <el-tooltip effect="dark" content="详情" placement="top" :enterable="false">
                        <el-button type="primary" size="small" @click="handleInfo(scope.row.id)"><el-icon><Postcard /></el-icon></el-button>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="2">
                      <!-- 配置按钮 -->
                      <el-tooltip effect="dark" content="配置" placement="top" :enterable="false">
                        <el-button type="warning" size="small" @click="handleSetting(scope.row)"><el-icon><setting /></el-icon></el-button>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="2">
                      <!-- 删除按钮 -->
                      <el-popconfirm title="此操作将永久删除, 是否继续?" @confirm="sendDelImage(scope.row.id)">
                        <template #reference>
                          <div>
                            <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                              <el-button type="danger" size="small"><el-icon><delete /></el-icon></el-button>
                            </el-tooltip>
                          </div>
                        </template>
                      </el-popconfirm>
                    </el-col>
                  </el-space>
                </el-row>
              </template>
            </el-table-column>
          </el-table>

          <!-- form -->
          <el-dialog
              v-model="uploadImageVis"
              title="上传镜像"
              width="30%"
          >
            <el-upload
                class="upload-demo"
                accept=".tar"
                :action="upload.url"
                :headers="upload.headers"
                :limit="1"
                :on-success="uploadSuccess"
                :on-progress="uploading"
                :before-upload="beforeUpload"
                :on-remove="removeUpload"
                drag
                multiple
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                拖动文件到此处或者 <em>点击上传</em>
              </div>
            </el-upload>
          </el-dialog>

          <el-dialog
              v-model="inspectVis"
              title="镜像信息"
              width="50%"
              draggable
          >
            <json-viewer :value="imageInspectInfo" copyable boxed sort />
          </el-dialog>

          <el-dialog
              v-model="editTagVis"
              title="修改镜像Tag"
              width="30%"
          >
            <form-create :rule="editTagFormRule" :option="submitClose"></form-create>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="editTagSubmit">提交</el-button>
                <el-button @click="addTagInput">新增镜像tag</el-button>
              </span>
            </template>
          </el-dialog>

        </el-card>
      </div>
    </div>
  </div>

</template>

<script>
import { getImages } from '../../util/doRequest';

export default {
  name: 'imageManage',
  data() {
    return {
      image: '', // 点击任一操作按钮时选中的image.
      imageList: [], // 所有镜像信息.
      selection: '', // 表格中被选中项
      uploadImageVis: false, // 上传镜像表单
      inspectVis: false, // 镜像inspect表单显示
      imageInspectInfo: '', // 镜像inspect信息
      loadingVis: false, // 上传时渲染加载
      editTagVis: false, // 镜像Tag编辑表单
      // 上传镜像的设置项
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: `Bearer ${window.sessionStorage.getItem('token')}` },
        // 上传的地址
        url: `${location.protocol}//${location.hostname}:8082/v1/images/load`,
        // url: `${location.protocol}//************:8082/v1/images/load`,
        // 上传的文件列表
        fileList: [],
      },
      editTagFormRule: [],
      submitClose: {
        submitBtn: false,
      },
    };
  },
  created() {
    this.getImageList();
  },
  methods: {
    /**
     * 获取镜像.
     */
    getImageList() {
      getImages(this.$http, (res, error) => {
        if (res) {
          this.imageList = res;
        } else if (error) {
          console.log('get image error', error);
        }
      });
    },
    /**
     * 表格的选中事件.
     * @param val .
     */
    selectionChange(val) {
      this.selection = val;
    },
    /**
     * 读取本地文件时的钩子.
     */
    uploading() {
      console.log('正在上传镜像');
      if (!this.loadingVis) {
        this.loadingVis = true;
      }
    },
    /**
     * 调用上传操作完成时的钩子.
     */
    uploadSuccess() {
      ElMessage.success('上传镜像成功，等待文件被解析后才会显示');
      this.loadingVis = false;
      this.uploadImageVis = false;
      this.getImageList();
    },
    /**
     * 上传文件前的钩子，慢于uploadChange的调用.
     */
    beforeUpload() {
      ElMessage.info('上传过程中请不要离开此页面');
    },
    /**
     * 文件列表移除文件时的钩子.
     */
    removeUpload() {
      ElMessage.info('取消镜像上传');
      this.loadingVis = false;
    },
    /**
     * 删除镜像.
     */
    delImage() {
      if (this.selection.length <= 0) {
        ElMessage.info('请先选中镜像');
        return;
      }
      ElMessageBox.confirm('此操作将永久删除该镜像, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.selection.forEach((select) => {
          this.sendDelImage(select.id);
        });
      }).catch(() => {
        ElMessage.info('取消删除');
      });
    },
    /**
     * 获取镜像信息.
     * @param imageId 镜像Id.
     */
    handleInfo(imageId) {
      this.inspectVis = true;
      const url = this.$SERVICE_PLATFORM.serverApi.imageInspect.replace('%s', imageId);
      this.$http.get(url).then((res) => {
        this.imageInspectInfo = res.data;
      }).catch((error) => {
        ElMessage.error('获取镜像信息失败');
        console.log(error);
      });
    },
    /**
     * 修改镜像Tag
     * @param image 镜像.
     */
    handleSetting(image) {
      this.editTagVis = true;
      this.editTagFormRule = [];
      this.image = image;
      image.tag.forEach((nameTag) => {
        this.editTagFormRule.push({
          type: 'input',
          field: 'name',
          title: '镜像tag',
          value: nameTag,
          props: {
            readonly: true,
          },
        });
      });
    },
    /**
     * 删除镜像请求.
     * @param imageId 镜像Id.
     */
    sendDelImage(imageId) {
      const url = this.$SERVICE_PLATFORM.serverApi.imageDel.replace('%s', imageId);
      this.$http.delete(url).then(() => {
        ElMessage.success('删除完成');
        this.getImageList();
      }).catch((error) => {
        ElMessage.error('删除失败，该镜像已被使用');
        console.log(error);
      });
    },
    /**
     * 提交新增的镜像tag.
     */
    editTagSubmit() {
      const newTags = []; // 需要追加的新tag
      if (this.editTagFormRule.length === 1) {
        ElMessage.info('请输入新的tag标识');
        return;
      }
      this.editTagFormRule.some((rule) => {
        const reg = /[a-zA-Z0-9]+:[a-zA-Z0-9]+/; // xx:xx正则
        console.log('tag reg', rule.value, reg.test(rule.value));
        if (!rule.value || !reg.test(rule.value)) {
          ElMessage.error('tag格式错误或已存在');
          return true;
        }
        if (this.image.tag.indexOf(rule.value) < 0) {
          console.log('add a new tag:', rule.value);
          newTags.push(rule.value);
        }
        return false;
      });

      newTags.forEach((tag) => {
        const url = this.$SERVICE_PLATFORM.serverApi.imageTag.replace('%s', this.image.id);
        const r = tag.split(':')[0];
        const t = tag.split(':')[1];
        this.$http.post(url, null, {
          params: {
            repo: r,
            tag: t,
          },
        }).then(() => {
          ElMessage.success('添加新的tag成功');
          getImageList();
        });
      });
      this.editTagVis = false;
    },
    /**
     * 追加一个或多个tag.
     */
    addTagInput() {
      this.editTagFormRule.push({
        type: 'input',
        field: 'name',
        title: '镜像tag',
        // 验证规则
        validate: [
          {
            required: true,
            message: '请输入tag名，格式为xxx:xxx',
            trigger: 'blur',
          },
        ],
      });
    },
  },
};
</script>

<style scoped>

</style>
