<template>
  <div>
    <div class="app-container">
      <div class="filter-container">
        <!-- 卡片视图区域 -->
        <el-card>
          <template #header>
            <div class="header">
              <el-button type="primary" @click="containerVisible">创建容器</el-button>
              <el-button type="primary" @click="addStackVis = true">部署堆栈</el-button>
              <el-button type="success" @click="startContainer">启动</el-button>
              <el-button @click="stopContainer">停止</el-button>
              <el-button @click="restartContainer">重启</el-button>
              <el-button type="danger" @click="forceDelContainer">强制删除</el-button>
              <el-button type="primary" @click="getContainers">刷新数据</el-button>
            </div>
          </template>
          <!-- 容器列表区域  -->
          <el-table :data="containerList" row-key="id" height="600" style="width: 100%" border stripe @selection-change="containerSelectionChange">
            <el-table-column fixed type="index" />
            <el-table-column fixed type="selection" width="50" />
            <el-table-column property="name" label="容器名" sortable />
            <el-table-column property="image" label="镜像" sortable />
            <el-table-column property="created" label="创建时间" sortable />
            <el-table-column property="address" label="IP" sortable />
            <el-table-column property="status" label="状态" background-color="#ccc" sortable />
            <el-table-column label="操作" width="205px">
              <template v-slot="scope">
                <!-- 详情按钮 -->
                <span v-if="scope.row.type === types.container">
                  <el-tooltip effect="dark" content="详情" placement="top" :enterable="false">
                    <el-button type="primary" size="small" @click="containerInspect(scope.row)"
                      ><el-icon><Postcard /></el-icon
                    ></el-button>
                  </el-tooltip>
                  <!-- 修改按钮 -->
                  <el-tooltip effect="dark" content="修改" placement="top" :enterable="false">
                    <el-button type="primary" size="small" @click="handleEdit(scope.row)"
                      ><el-icon><edit /></el-icon
                    ></el-button>
                  </el-tooltip>
                  <!-- 配置按钮 -->
                  <el-tooltip effect="dark" content="配置" placement="top" :enterable="false">
                    <el-button type="warning" size="small" @click="handleSetting(scope.row)"
                      ><el-icon><setting /></el-icon
                    ></el-button>
                  </el-tooltip>
                  <!-- 删除按钮 -->
                  <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                    <el-button type="danger" size="small" @click="handleDelete(scope.row)"
                      ><el-icon><delete /></el-icon
                    ></el-button>
                  </el-tooltip>
                </span>
                <span v-else>
                  <el-tooltip effect="dark" content="详情" placement="top" :enterable="false">
                    <el-button type="primary" size="small" @click="stackConfig(scope.row)"
                      ><el-icon><Postcard /></el-icon
                    ></el-button>
                  </el-tooltip>
                </span>
              </template>
            </el-table-column>
          </el-table>

          <!-- form -->
          <el-dialog v-model="addContainerVisible" title="创建容器" width="40%" draggable>
            <!-- 内容主体区域 -->
            <el-form ref="containerFormRef" :model="createContainerForm" :rules="containerFormRule" label-width="80px">
              <!-- 固定项目 -->
              <el-form-item label="名称" prop="containerName">
                <el-input v-model="createContainerForm.containerName" placeholder="请输入容器名称"></el-input>
              </el-form-item>
              <el-form-item label="镜像" prop="image">
                <el-select v-model="createContainerForm.image" placeholder="请选择镜像">
                  <el-option v-for="item in imageList" :key="item" :label="item" :value="item"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="重启策略" prop="restartPolicy">
                <el-select v-model="createContainerForm.restartPolicy" placeholder="请选择重启策略">
                  <el-option v-for="item in policy" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="特权模式" prop="privileged">
                <el-switch v-model="createContainerForm.privileged"></el-switch>
              </el-form-item>
              <el-form-item label="存储映射" prop="privileged">
                <el-button type="info" size="small" @click="addVolumeOpt"
                  ><el-icon><Plus /></el-icon>映射附加存储卷</el-button
                >
              </el-form-item>
              <el-form-item label="映射卷" v-for="volume in createContainerForm.volumes" :key="volume.name">
                <span>
                  <el-input v-model="volume.containerPath" placeholder="请输入容器内的路径" style="display: inline"></el-input>
                  <el-button size="default" v-model="volume.mountOrBind" @click="mountOrBind(volume)" style="width: 60px">{{ volume.mountOrBind }}</el-button>
                  <el-icon><Link /></el-icon>
                  <el-select v-model="volume.volume" placeholder="请选择存储卷" v-if="volume.mountOrBind === 'Volume'">
                    <el-option v-for="item in volumeList" :key="item" :label="item" :value="item"></el-option>
                  </el-select>
                  <el-input v-model="volume.bind" placeholder="请输入宿主机的路径" style="display: inline" v-if="volume.mountOrBind === 'Bind'"></el-input>
                  <el-button size="default" v-model="volume.writableOrReadOnly" @click="writeOrRead(volume)" style="width: 75px">{{
                    volume.writableOrReadOnly
                  }}</el-button>
                  <el-button size="small" link @click="delVolumeOpt(volume)"
                    ><el-icon><delete /></el-icon
                  ></el-button>
                </span>
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="createContainer">提交</el-button>
                <el-button @click="addContainerVisible = false">取消</el-button>
              </span>
            </template>
          </el-dialog>

          <el-dialog v-model="addNetworkVis" title="增加网络" width="30%" draggable>
            <form-create :rule="netConnectRule" :option="resetOn" @submit="netConnectSubmit"></form-create>
          </el-dialog>

          <el-dialog v-model="addRouterVis" title="增加路由" width="30%" draggable>
            <form-create :rule="routeRule" :option="resetOn" @submit="routeSubmit"></form-create>
          </el-dialog>

          <el-dialog v-model="containerInfoEditVis" title="修改容器" width="30%" draggable>
            <form-create :rule="containerInfoRule" :option="resetOn" @submit="containerInfoEditSubmit"></form-create>
          </el-dialog>

          <el-dialog v-model="containerDelVis" title="删除容器" width="30%" draggable>
            <el-form>
              <el-form-item label="自动删除非持久化挂载卷" size="large">
                <el-switch v-model="containerVDel" />
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="containerDelCancel">取消</el-button>
                <el-button type="danger" @click="containerDelSubmit">删除</el-button>
              </span>
            </template>
          </el-dialog>

          <el-dialog v-model="containerInfoVis" title="容器信息" width="50%" draggable>
            <json-viewer :value="containerInspectInfo" copyable boxed sort edited />
          </el-dialog>

          <el-dialog v-model="stackInfoVis" title="堆栈信息" width="50%" draggable>
            <json-viewer :value="stackConfigInfo" copyable boxed sort edited />
          </el-dialog>

          <el-dialog v-model="addStackVis" title="部署堆栈" width="40%" draggable>
            <el-row :gutter="15" class="stack-row" style="height: 45px">
              <el-input class="stack-name" placeholder="请输入堆栈名称" v-model="stackName">
                <template #prepend>堆栈名称</template>
              </el-input>
            </el-row>
            <el-row :gutter="15" class="stack-row" v-for="(con, key) in stackInfo" :key="key">
              <el-input disabled v-model="con.containerName" class="input-with-select" :style="{ width: '84%' }">
                <template #prepend> 修改文件 </template>
                <template #append>
                  <el-select
                    v-model="con.imageTag"
                    placeholder="选择恢复备份的镜像"
                    style="background: white"
                    @change="composeImageChange($event, con.containerName)"
                  >
                    <el-option v-for="item in con.images" :key="item" :label="item" :value="item"> </el-option>
                  </el-select>
                </template>
              </el-input>
            </el-row>
            <el-row :gutter="15" class="stack-row">
              <el-upload
                ref="uploadStack"
                class="upload-demo"
                accept=".yaml"
                :action="upload.url"
                :headers="upload.headers"
                :file-list="upload.fileList"
                :limit="1"
                :on-exceed="handleExceed"
                :on-change="change"
                :on-remove="remove"
                :auto-upload="false"
              >
                <template #trigger>
                  <el-button type="primary">选择文件</el-button>
                </template>
                <el-space wrap>
                  <el-col :span="2"> </el-col>
                  <el-col :span="2">
                    <el-button class="ml-3" type="success" @click="submitUpload" :loading="addStackVisLoading"> 点击上传 </el-button>
                  </el-col>
                </el-space>
              </el-upload>
            </el-row>

            <el-row :gutter="15" class="stack-row">
              <el-switch v-model="ymlEditLock" active-text="启用编辑"></el-switch>
            </el-row>
            <codemirror
              v-model="yamlData"
              placeholder="在此处编辑docker-compose"
              :style="{ height: '300px', width: '100%' }"
              @change="codeChange('change', $event)"
              :disabled="!ymlEditLock"
            />
          </el-dialog>

          <el-drawer v-model="settingsVis">
            <template #header>
              <h4>[{{ container.name }}]容器配置</h4>
            </template>
            <el-row :gutter="15" class="drawer-card">
              <el-card>
                <template #header>
                  <div>网络配置</div>
                </template>
                <el-table :data="containerNetStatus" title="网络设置" border>
                  <el-table-column prop="containerID" label="容器名" width="120" />
                  <el-table-column prop="networkID" label="网卡" width="120" />
                  <el-table-column prop="ipaddress" label="IP" width="150" />
                  <el-table-column fixed="right" label="操作" width="120">
                    <template #default="scope">
                      <el-popconfirm
                        confirm-button-text="确认"
                        cancel-button-text="取消"
                        icon-color="red"
                        title="是否确认删除该网络?"
                        @confirm="deleteContainerNet(scope.row)"
                      >
                        <template #reference>
                          <el-button type="danger">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
                <span>
                  <el-button type="primary" @click="addNetwork">增加网络</el-button>
                  <el-button type="primary" @click="addRouter">设置默认路由</el-button>
                </span>
              </el-card>
            </el-row>
            <el-divider />
            <el-row :gutter="15" class="drawer-card">
              <el-card>
                <template #header>
                  <div>系统配置</div>
                </template>
                <el-space>
                  <el-button type="primary" @click="downloadLog"
                    ><el-icon><Download /></el-icon>下载日志</el-button
                  >
                  <el-button type="primary" @click="backupCon"
                    ><el-icon><CopyDocument /></el-icon>备份容器</el-button
                  >
                </el-space>
              </el-card>
            </el-row>
          </el-drawer>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from "element-plus";
import { isEqualIpAddress, isYaml, readerFile } from "../../util/checkFormat";
import { getContainerWithNet, getImages, getNetworks, getVolumes } from "../../util/doRequest";
import download from "../../util/download";
import { formatDate, json2yaml, yaml2json } from "../../util/transferFormat";

export default {
  name: "ContainerManage",
  data() {
    const checkName = (rule, value, callback) => {
      const reg = /^[a-zA-Z0-9_]{0,}$/;
      if (!value) {
        return callback(new Error("容器名称不能为空"));
      }
      if (!reg.test(value)) {
        return callback(new Error("请输入由英文字母、下划线及数字组成的字符串"));
      }
      callback();
    };
    return {
      // yarn add codemirror vue-codemirror
      // npm install codemirror vue-codemirror --save
      // yarn add @codemirror/lang-html
      // yarn add @codemirror/theme-one-dark
      yamlData: "",
      container: "", // 点击每一个行的操作按钮时，赋值给containerName
      containerSelection: "", // 表格中被选中项
      containerList: [], // 容器数据
      types: {
        stack: "stack",
        container: "container",
      },
      operation: {
        start: {
          name: "start",
          des: "启动",
        },
        stop: {
          name: "stop",
          des: "停止",
        },
        restart: {
          name: "restart",
          des: "重启",
        },
        del: {
          name: "forceDel",
          des: "强制删除",
        },
      },
      stackInfo: [
        // {
        //   containerName: '',
        //   imageTag: '',
        //   images: [],
        // },
      ],
      stackName: "",
      ymlEditLock: false,
      imageList: [], // 镜像数据
      volumeList: [], // 存储卷
      networkObj: {}, // 网络
      // 重启策略
      policy: [
        { label: "从不", value: "no" },
        { label: "总是", value: "always" },
        { label: "非正常退出时重启", value: "on-failure" },
        { label: "总是重启排除崩溃", value: "unless-stopped" },
      ],
      addContainerVisible: false, // 容器表单显示
      containerInfoEditVis: false, // 容器信息修改表单显示
      containerDelVis: false, // 容器删除表单显示
      settingsVis: false, // 容器配置
      addNetworkVis: false, // 增加网络表单显示
      addRouterVis: false, // 增加容器路由表单显示
      addStackVis: false, // 创建堆栈表单显示
      addStackVisLoading: false, // 创建堆栈表单Loading状态
      containerNetStatus: [], // 容器网络连接状态
      containerInfoVis: false,
      containerInspectInfo: "",
      stackInfoVis: false,
      stackConfigInfo: "",
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: `Bearer ${window.sessionStorage.getItem("token")}` },
        // 上传的地址
        url: "",
        // 上传的文件
        fileList: [],
      },
      // 创建容器的表单
      createContainerForm: {
        containerName: "", // 容器名称
        image: "", // 镜像
        restartPolicy: "", // 重启策略
        privileged: true, // 特权模式
        volumes: [
          // seq: ,
          // mountOrBind: 'Volume', // 映射挂载卷或者绑定宿主机
          // containerPath: '', // 容器内路径
          // volume: '', // 挂载卷
          // bind: '', // 宿主机绑定路径
          // writableOrReadOnly: 'Writable',
        ],
      },
      // 创建容器的表单规则
      containerFormRule: {
        containerName: [{ required: true, validator: checkName, trigger: "blur" }],
        image: [{ required: true, message: "请选择镜像", trigger: "blur" }],
        restartPolicy: [{ required: true, message: "请选择重启策略", trigger: "blur" }],
      },

      // 修改容器的表单规则
      containerInfoRule: [
        {
          type: "input",
          field: "name",
          title: "容器名称",
          // 验证规则
          validate: [
            {
              required: true,
              message: "请输入容器名",
              trigger: "blur",
            },
            {
              min: 1,
              max: 20,
              message: "长度在 1 - 20个字符之间",
              trigger: "blur",
            },
            {
              pattern: /^\w+$/,
              message: "仅支持英文数字下划线输入",
            },
          ],
        },
      ],
      // 创建网络连接的表单规则
      netConnectRule: [
        {
          type: "select",
          field: "networkID",
          title: "网络名称",
          options: [],
          validate: [
            {
              required: true,
              message: "请选择网卡",
              trigger: true,
            },
          ],
        },
        {
          type: "input",
          field: "containerID",
          title: "容器名称",
          value: "",
          props: {
            readonly: true,
          },
        },
        {
          type: "input",
          field: "ipaddress",
          title: "IPV4地址",
          validate: [
            {
              required: true,
              message: "请输入Ip地址",
              trigger: true,
            },
            {
              pattern:
                /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
              message: "请输入正确的Ip地址格式",
            },
          ],
        },
      ],
      // 创建路由的表单规则
      routeRule: [
        {
          type: "input",
          field: "router",
          title: "默认路由",
          validate: [
            {
              required: true,
              message: "请输入默认路由地址",
              trigger: true,
            },
            {
              pattern:
                /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
              message: "请输入正确的Ip地址格式",
            },
          ],
        },
      ],
      // 删除容器删除容器挂载卷
      containerVDel: false,
      // 表单中的显示重置按钮
      resetOn: {
        resetBtn: true,
      },
      // 表单中隐藏提交按钮
      submitClose: {
        submitBtn: false,
        resetBtn: false,
      },
    };
  },
  watch: {
    yamlData(newValue, oldValue) {
      this.stackInfo = [];
      const jsonData = yaml2json(newValue);
      if (jsonData.error) {
        return;
      }
      Object.keys(jsonData.data.services).forEach((key) => {
        const tags = [];
        const tag = jsonData.data.services[key].image;
        const name = jsonData.data.services[key].container_name;
        tags.push(tag);
        this.imageList.forEach((imageName) => {
          if (imageName.split(":")[0].replace("-", ":") === tag) {
            tags.push(imageName);
          }
        });
        this.stackInfo.push({
          containerName: name,
          imageTag: tag,
          images: tags,
        });
      });
    },
  },
  created() {
    this.getContainers();
    this.getImageList();
    // 初始化挂载卷信息
    getVolumes(this.$http, (res) => {
      res.forEach((volumeData) => {
        this.volumeList.push(volumeData.name);
      });
    });
    // 初始化网络信息
    getNetworks(this.$http, (data) => {
      data.forEach((net) => {
        this.networkObj[net.name] = net;
      });
      this.netConnectRule.some((rule) => {
        if (rule.field === "networkID") {
          Object.keys(this.networkObj).forEach((key) => {
            rule.options.push({ value: this.networkObj[key].name, label: this.networkObj[key].name });
          });
          return true;
        }
        return false;
      });
    });
  },
  methods: {
    /**
     * 读取容器.
     * @returns {Promise<void>}
     */
    async getContainers() {
      await this.$http
        .get(this.$SERVICE_PLATFORM.serverApi.containers)
        .then((res) => {
          this.containerList = [];
          Object.keys(res.data).forEach((key) => {
            res.data[key].forEach((container) => {
              const ip = [];
              Object.keys(container.NetworkSettings.Networks).forEach((netName) => {
                ip.push(container.NetworkSettings.Networks[netName].IPAddress);
              });
              const rowDataOfCon = {
                name: container.Names[0].replace("/", ""),
                image: container.Image,
                type: this.types.container,
                created: formatDate(container.Created * 1000),
                address: ip,
                status: container.Status,
              };
              if (key) {
                let notExistStack = true;
                this.containerList.forEach((con) => {
                  if (con.name === key && con.type === this.types.stack) {
                    con.children.push(rowDataOfCon);
                    notExistStack = false;
                  }
                });
                if (notExistStack) {
                  const child = [];
                  child.push(rowDataOfCon);
                  this.containerList.push({
                    id: key,
                    name: key,
                    type: this.types.stack,
                    children: child,
                  });
                }
              } else {
                this.containerList.push(rowDataOfCon);
              }
            });
          });
        })
        .catch((error) => {
          ElMessage.error(error.toString());
        });
    },
    /**
     * 读取镜像
     * @returns {Promise<void>}
     */
    getImageList() {
      getImages(this.$http, (res, error) => {
        if (res) {
          res.forEach((image) => {
            image.tag?.forEach((tag) => {
              this.imageList.push(tag);
            });
          });
        } else if (error) {
          console.log("get image error", error);
        }
      });
    },
    /**
     * 根据容器Id启动容器.
     */
    startContainer() {
      if (this.containerSelection.length <= 0) {
        ElMessage.info("请先选中容器");
        return;
      }
      const setStackCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.stackStart,
        method: "post",
        opt: this.operation.start,
      };
      const setContainerCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.containerStart,
        method: "post",
        opt: this.operation.start,
      };
      this.cmdForObject(setStackCmd, setContainerCmd);
    },
    /**
     * 根据容器Id停止容器.
     */
    stopContainer() {
      if (this.containerSelection.length <= 0) {
        ElMessage.info("请先选中容器");
        return;
      }
      const setStackCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.stackStop,
        method: "post",
        opt: this.operation.stop,
      };
      const setContainerCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.containerStop,
        method: "post",
        opt: this.operation.stop,
      };
      this.cmdForObject(setStackCmd, setContainerCmd);
    },
    /**
     * 根据容器Id重启容器.
     */
    restartContainer() {
      if (this.containerSelection.length <= 0) {
        ElMessage.info("请先选中容器");
        return;
      }
      const setStackCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.stackRestart,
        method: "post",
        opt: this.operation.restart,
      };
      const setContainerCmd = {
        url: this.$SERVICE_PLATFORM.serverApi.containerRestart,
        method: "post",
        opt: this.operation.restart,
      };
      this.cmdForObject(setStackCmd, setContainerCmd);
    },
    /**
     * 强制删除容器.
     */
    forceDelContainer() {
      if (this.containerSelection.length <= 0) {
        ElMessage.info("请先选中容器");
        return;
      }
      ElMessageBox.confirm("此操作将永久删除该容器及挂载卷, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const setStackCmd = {
            url: this.$SERVICE_PLATFORM.serverApi.stackRemove,
            method: "post",
            opt: this.operation.del,
          };
          const setContainerCmd = {
            url: this.$SERVICE_PLATFORM.serverApi.containerDel,
            method: "delete",
            opt: this.operation.del,
          };
          this.cmdForObject(setStackCmd, setContainerCmd);
        })
        .catch(() => {
          ElMessage.info("取消删除");
        });
    },
    /**
     * 显示创建容器表单，并将添加存储卷项初始为空.
     */
    containerVisible() {
      this.addContainerVisible = true;
      this.createContainerForm.volumes = [];
    },
    /**
     * 创建新容器.
     * @param formData.
     */
    createContainer() {
      this.$refs.containerFormRef.validate((val) => {
        if (val) {
          // 如果校验为真，则提交表单
          const mounts = [];
          const volumes = {};
          this.createContainerForm.volumes.forEach((vol) => {
            let type = "";
            let tar = "";
            volumes[vol.containerPath] = {};
            if (vol.volume) {
              type = "volume";
              tar = vol.volume;
            }
            if (vol.bind) {
              type = "bind";
              tar = vol.bind;
            }
            mounts.push({
              Type: type,
              Source: tar,
              Target: vol.containerPath,
              ReadOnly: vol.writableOrReadOnly !== "Writable",
            });
          });

          const data = {
            Image: this.createContainerForm.image,
            Volumes: volumes,
            HostConfig: {
              RestartPolicy: {
                Name: this.createContainerForm.restartPolicy,
              },
              Mounts: mounts,
              Privileged: this.createContainerForm.privileged,
            },
          };

          const url = this.$SERVICE_PLATFORM.serverApi.containerCreate;
          this.$http
            .post(url, data, {
              params: {
                name: this.createContainerForm.containerName,
              },
            })
            .then(() => {
              ElMessage.success("创建容器成功");
              this.getContainers();
            })
            .catch((error) => {
              ElMessage.error("获取容器网络信息失败");
            });
          this.addContainerVisible = false;
          this.$refs.containerFormRef.resetFields();
        } else {
          // 校验失败，返回对应的message
          console.log("校验不通过", val);
        }
      });
    },
    /**
     * 创建容器的表单中添加映射存储卷.
     */
    addVolumeOpt() {
      let len = this.createContainerForm.volumes.length;
      if (len === 0) {
        len += 1;
      }
      this.createContainerForm.volumes.push({
        seq: len,
        mountOrBind: "Volume", // 映射挂载卷或者绑定宿主机
        containerPath: "", // 容器内路径
        volume: "", // 挂载卷
        bind: "", // 宿主机绑定路径
        writableOrReadOnly: "Writable",
      });
    },
    mountOrBind(volume) {
      if (volume.mountOrBind === "Volume") {
        volume.mountOrBind = "Bind";
      } else if (volume.mountOrBind === "Bind") {
        volume.mountOrBind = "Volume";
      }
    },
    writeOrRead(volume) {
      if (volume.writableOrReadOnly === "Writable") {
        volume.writableOrReadOnly = "Read-Only";
      } else if (volume.writableOrReadOnly === "Read-Only") {
        volume.writableOrReadOnly = "Writable";
      }
    },
    /**
     * 删除映射卷的选项.
     * @param volume 指定删除的选项.
     */
    delVolumeOpt(volume) {
      for (let i = 0; i < this.createContainerForm.volumes.length; i += 1) {
        if (this.createContainerForm.volumes[i].seq === volume.seq) {
          this.createContainerForm.volumes.splice(i, 1);
          return;
        }
      }
    },
    /**
     * inspect容器.
     * @param row.
     */
    containerInspect(row) {
      this.containerInfoVis = true;
      const url = this.$SERVICE_PLATFORM.serverApi.containerInspect.replace("%s", row.name);
      this.$http
        .get(url)
        .then((res) => {
          this.containerInspectInfo = res.data;
        })
        .catch((error) => {
          ElMessage.error("获取容器信息失败");
          console.error(error);
        });
    },
    /**
     * 修改按钮点击事件处理.
     * @param row
     */
    handleEdit(row) {
      this.containerInfoEditVis = true;
      this.container = row;
      this.containerInfoRule.forEach((rule) => {
        if (rule.field === "name") {
          rule.value = row.name;
        }
      });
    },
    /**
     * 删除容器按钮点击事件处理.
     * @param row
     */
    handleDelete(row) {
      this.containerVDel = false;
      this.container = row;
      this.containerDelVis = true;
    },
    /**
     * 配置按钮点击事件处理.
     * @param row 当前行数据.
     */
    handleSetting(row) {
      this.settingsVis = true;
      this.container = row;
      getContainerWithNet(this.$http, row.name, (res) => {
        if (res.status === 200) {
          this.containerNetStatus = res.data;
        } else {
          ElMessage.error("获取容器网络信息失败");
        }
      });
    },
    /**
     * 读取堆栈信息.
     * @param row .
     */
    stackConfig(row) {
      this.$http
        .get(this.$SERVICE_PLATFORM.serverApi.stackConfig, {
          params: {
            name: row.name,
          },
        })
        .then((res) => {
          this.stackConfigInfo = res.data;
          this.stackInfoVis = true;
        })
        .catch((error) => {
          ElMessage.error("获取堆栈信息失败");
        });
    },
    /**
     * 表格的选中事件.
     * @param val .
     */
    containerSelectionChange(val) {
      this.containerSelection = val;
    },
    /**
     * 容器增加网络连接表单.
     */
    addNetwork() {
      this.addNetworkVis = true;
      this.netConnectRule.some((rule) => {
        if (rule.field === "containerID") {
          rule.value = this.container.name;
          return true;
        }
        return false;
      });
    },

    /**
     * 修改容器信息.
     * @param formData .
     */
    containerInfoEditSubmit(formData) {
      if (this.container.name === formData.name) {
        ElMessage.info("请填写新容器名");
        return;
      }
      const url = this.$SERVICE_PLATFORM.serverApi.containerRename.replace("%s", this.container.name);
      this.$http
        .post(url, null, {
          params: {
            name: formData.name,
          },
        })
        .then(() => {
          ElMessage.success("修改成功");
          this.getContainers();
        })
        .catch((error) => {
          ElMessage.error("修改失败");
        });
      this.containerInfoEditVis = false;
    },
    /**
     * 提交网络连接配置.
     * @param formData
     */
    netConnectSubmit(formData) {
      const url = this.$SERVICE_PLATFORM.serverApi.netConnectContainer.replace("%s", formData.networkID);
      const connectNet = this.networkObj[formData.networkID]; // 指定的连接网络的信息
      const nets = connectNet.subnet.split(",");
      let contain = false;
      nets.some((net) => {
        const parentIp = net.split("/")[0]; // 指定网络的Ip
        const mask = net.split("/")[1]; // 指定网络的掩码
        if (isEqualIpAddress(formData.ipaddress, parentIp, mask)) {
          contain = true;
          return true;
        }
        return false;
      });

      if (contain) {
        this.$http
          .post(url, JSON.stringify(formData))
          .then((res) => {
            if (res) {
              ElMessage.success("成功连接");
              getContainerWithNet(this.$http, formData.containerID, (response) => {
                if (response) {
                  this.containerNetStatus = response.data;
                  this.addNetworkVis = false;
                } else {
                  ElMessage.error("获取容器网络信息失败");
                }
              });
            }
          })
          .catch((error) => {
            ElMessage.error("设置容器网络失败");
            console.log(error);
          });
      } else {
        ElMessage.error("输入的Ip与指定的网络不在同一网段");
      }
    },

    /**
     * 删除容器网络连接.
     * @param row
     */
    deleteContainerNet(row) {
      const url = this.$SERVICE_PLATFORM.serverApi.delContainerNet.replace("%s", row.networkID);
      this.$http.post(url, { containerID: row.containerID }).then(() => {
        ElMessage.success("成功断开连接");
        this.getContainers();
        getContainerWithNet(this.$http, row.containerID, (response) => {
          if (response.status === 200) {
            this.containerNetStatus = response.data;
          } else {
            ElMessage.error("获取容器网络信息失败");
          }
        });
      });
    },
    /**
     * 增加路由.
     * @param row
     */
    addRouter() {
      console.log("route", this.container);
      this.addRouterVis = true;
      this.routeRule.some((rule) => {
        if (rule.field === "router") {
          rule.value = "";
          const url = this.$SERVICE_PLATFORM.serverApi.addContainerRouter.replace("%s", this.container.name);
          this.$http
            .get(url)
            .then((res) => {
              rule.value = res.data.defaultRoute;
            })
            .catch((error) => {
              console.log("获取默认路由失败", error);
            });
          return true;
        }
        return false;
      });
    },
    /**
     * 提交设置路由表单
     */
    routeSubmit(formData) {
      const url = this.$SERVICE_PLATFORM.serverApi.addContainerRouter.replace("%s", this.container.name);
      this.$http
        .put(url, null, {
          params: {
            ip: formData.router,
          },
        })
        .then(() => {
          ElMessage.success("添加默认路由成功");
          this.addRouterVis = false;
        })
        .catch((error) => {
          ElMessage.error("添加默认路由失败");
          console.log("添加默认路由失败", error);
        });
    },
    /**
     * 取消删除容器.
     */
    containerDelCancel() {
      this.containerDelVis = false;
    },
    /**
     * 提交删除容器请求.
     */
    containerDelSubmit() {
      const url = this.$SERVICE_PLATFORM.serverApi.containerDel.replace("%s", this.container.name);
      console.log(this.containerVDel);
      this.$http
        .delete(url, {
          params: {
            v: this.containerVDel,
          },
        })
        .then((res) => {
          if (res.status >= 200 && res.status < 300) {
            ElMessage.success("删除容器及挂载卷完成");
            this.getContainers();
          }
        })
        .catch((error) => {
          if (error.response.status === 400) {
            ElMessage.error("请先停止容器，再执行删除操作");
          }
        });
      this.containerDelVis = false;
    },
    /**
     * 超出限制时的钩子函数.
     * @param {array} fileList 文件列表.
     */
    handleExceed(fileList) {
      if (!isYaml(fileList[0].name)) {
        ElMessage.error("只能上传yaml格式的文件！");
        this.upload.fileList = [];
        return false;
      }
      this.upload.fileList = fileList;
      this.setYamlData(fileList[0]);
      return true;
    },

    change(file) {
      if (!isYaml(file.name)) {
        ElMessage.error("只能上传yaml格式的文件！");
        this.upload.fileList = [];
        return false;
      }
      this.setYamlData(file);
      return true;
    },

    remove() {
      this.yamlData = null;
    },

    async setYamlData(file) {
      if (file.raw) {
        this.yamlData = await readerFile(file.raw);
      } else {
        this.yamlData = await readerFile(file);
      }
    },

    /**
     * 提交docker-compose.
     */
    async submitUpload() {
      if (!this.stackName) {
        ElMessage.info("请先输入堆栈名称");
        return;
      }
      if (!this.yamlData) {
        ElMessage.info("请选择docker-compose文件");
        return;
      }
      if (yaml2json(this.yamlData).error) {
        ElMessage.error("docker-compose格式错误！");
        return;
      }
      const exec = () => {
        const blob = new Blob([this.yamlData]);
        const file = new File([blob], "docker-compose.yaml");
        const form = new FormData();
        form.append("file", file);
        this.addStackVisLoading = true;
        this.$http
          .post(this.$SERVICE_PLATFORM.serverApi.stackCreate, form, {
            params: {
              name: this.stackName,
            },
          })
          .then((res) => {
            if (res.status === 200) {
              ElMessage.success("部署堆栈成功");
              this.yamlData = "";
              this.upload.fileList = [];
              this.getContainers();
            }
            this.addStackVis = false;
          })
          .catch((error) => {
            ElMessage.error(`部署堆栈失败 -> ${error.response?.data?.message || error.toString()}`);
          })
          .finally(() => {
            this.addStackVisLoading = false;
          });
      };

      await this.$http.get(this.$SERVICE_PLATFORM.serverApi.stackJson).then((res) => {
        const data = res.data || [];
        if (data.find((x) => x.Name === this.stackName)) {
          ElMessageBox({
            title: "注意",
            message: "堆栈名称已存在，上传后会覆盖配置",
            type: "warning",
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            showCancelButton: true,
          })
            .then(() => exec())
            .catch(() => {});
        } else {
          exec();
        }
      });
    },

    codeChange(mode, event) {
      if (yaml2json(event).error) {
        ElMessage.error("格式错误!");
      }
    },

    /**
     * 根据表格中选中的数据分别执行对应的api.
     * @param {object} stackApi 操作堆栈的API.
     * @param {object} containerApi 操作容器的API.
     */
    cmdForObject(stackApi, containerApi) {
      const notIncludeContainer = [];
      let config = {};
      if (stackApi) {
        this.containerSelection.forEach((obj) => {
          if (obj.type === this.types.stack) {
            obj.children.forEach((con) => {
              notIncludeContainer.push(con.name);
            });
            if (stackApi.opt.name) {
              config = {
                params: {
                  name: obj.name,
                },
              };
            }
            this.$http[stackApi.method](stackApi.url, {}, config)
              .then(() => {
                ElMessage.success(`${stackApi.opt.des}堆栈成功`);
                this.getContainers();
              })
              .catch((error) => {
                ElMessage.error(`${stackApi.opt.des}堆栈失败 -> ${error.toString()}`);
              });
          }
        });
      }
      if (containerApi) {
        let settings = {};
        this.containerSelection.forEach((obj) => {
          if (obj.type === this.types.container && notIncludeContainer.indexOf(obj.name) < 0) {
            const url = containerApi.url.replace("%s", obj.name);
            if (containerApi.opt.name === this.operation.del.name) {
              settings = {
                params: {
                  v: true,
                  force: true,
                },
              };
            }
            this.$http[containerApi.method](url, settings)
              .then(() => {
                ElMessage.success(`${containerApi.opt.des}容器成功`);
                this.getContainers();
              })
              .catch((error) => {
                ElMessage.error(`${containerApi.opt.des}容器失败 -> ${error.toString()}`);
              });
            settings = {};
          }
        });
      }
    },
    /**
     * 下载日志.
     */
    downloadLog() {
      const url = this.$SERVICE_PLATFORM.serverApi.containerLogs.replace("%s", this.container.name);
      this.$http
        .get(url)
        .then((res) => {
          download.downloadTar(res.data, `${this.container.name}-log-${formatDate(new Date())}`, "tar");
          ElMessage.success("下载容器日志成功");
        })
        .catch((error) => {
          if (error.response.status === 406) {
            ElMessage.warning("容器日志路径未设置");
          } else {
            ElMessage.error("下载容器日志失败!");
          }
        });
    },
    /**
     * 备份容器.
     */
    backupCon() {
      const url = this.$SERVICE_PLATFORM.serverApi.containerBackup.replace("%s", this.container.name);
      this.$http
        .post(url)
        .then((res) => {
          const imageTag = res.data.RepoTags[0];
          ElMessageBox.alert(`备份容器成功, 生成的镜像为[${imageTag}]`, "备份", {
            confirmButtonText: "确认",
          });
        })
        .catch((error) => {
          ElMessage.error("备份容器失败!");
        });
    },

    /**
     * 根据select选中项，修改codemirror中的镜像tag.
     * @param event 更改的数值.
     * @param containerName 对应的容器.
     */
    composeImageChange(event, containerName) {
      const compose = yaml2json(this.yamlData).data;
      Object.keys(compose.services).forEach((key) => {
        if (compose.services[key].container_name === containerName) {
          compose.services[key].image = event;
        }
      });
      this.yamlData = json2yaml(compose).data;
    },
  },
};
</script>

<style scoped>
.stack-name {
  padding-bottom: 20px;
  width: 55%;
}
.drawer-card {
  padding: 10px;
}
.stack-row {
  padding: 5px;
}
</style>
