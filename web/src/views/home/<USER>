<template>
  <el-container class="common-layout">
    <!--      <el-aside :width="isCollapse ? '64px':'200px'" >-->
    <el-aside width="auto">
      <!-- 侧边栏菜单区域 -->
      <el-menu
        default-active="2"
        class="el-menu-vertical-demo"
        active-text-color="#f08519"
        background-color="#545c64"
        text-color="#fff"
        :router="true"
        unique-opened
        :collapse="isCollapse"
        :collapse-transition="true"
      >
        <div class="aside-button">
          <img class="coryLogo" v-if="!isCollapse" :src="`${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.pictures.coryLogo.url}`" />
          <img class="corpLogoMini" v-else :src="`${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.pictures.corpLogoMini.url}`" />
        </div>
        <!--1级菜单-->
        <el-sub-menu index="/status" v-if="meterList.length > 0">
          <template #title>
            <el-icon><location /></el-icon>
            <span>仪盘表</span>
          </template>
          <el-menu-item-group>
            <el-menu-item :index="item.path" v-for="item in meterList" :key="item.name">
              <template #title>
                <el-icon>
                  <component :is="item.meta.icon" />
                </el-icon>
                <span>{{ item.meta.title }}</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>
        <!--1级菜单-->
        <el-sub-menu index="1" v-if="serviceManageList.length > 0">
          <template #title>
            <el-icon><grid /></el-icon>
            <span>服务管理</span>
          </template>
          <!--2级菜单-->
          <el-menu-item-group>
            <el-menu-item :index="item.path" v-for="item in serviceManageList" :key="item.name">
              <template #title>
                <el-icon>
                  <component :is="item.meta.icon" />
                </el-icon>
                <span>{{ item.meta.title }}</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>
        <!--1级菜单-->
        <el-sub-menu index="2" v-if="userManageList.length > 0">
          <template #title>
            <el-icon><user-filled /></el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item-group>
            <el-menu-item :index="item.path" v-for="item in userManageList" :key="item.name">
              <template #title>
                <el-icon>
                  <component :is="item.meta.icon" />
                </el-icon>
                <span>{{ item.meta.title }}</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>

        <el-sub-menu index="3" v-if="systemSettings.length > 0">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </template>
          <el-menu-item-group>
            <el-menu-item :index="item.path" v-for="item in systemSettings" :key="item.name">
              <template #title>
                <el-icon>
                  <component :is="item.meta.icon" />
                </el-icon>
                <span>{{ item.meta.title }}</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-sub-menu>
      </el-menu>
    </el-aside>
    <el-container>
      <el-header>
        <div>
          <el-icon class="toggle-button" @click="toggleCollapse" v-if="isCollapse">
            <Expand />
          </el-icon>
          <el-icon class="toggle-button" @click="toggleCollapse" v-else>
            <Fold />
          </el-icon>
          <el-breadcrumb separator-icon="ArrowRight">
            <el-breadcrumb-item :to="{ path: '/system' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ parentName }}</el-breadcrumb-item>
            <el-breadcrumb-item v-for="item in breakList" :key="item.path">
              {{ item.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div style="justify-content: right">
          <el-dropdown trigger="click">
            <el-icon class="dropdown-button" :size="20"><User /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="userInfo">{{ userInfo.username }}</el-dropdown-item>
                <el-dropdown-item @click="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown trigger="click">
            <el-icon class="dropdown-button" :size="20"><More /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="sysShutdown">
                  <el-icon :size="15"><SwitchButton /></el-icon>
                  关机
                </el-dropdown-item>
                <el-dropdown-item @click="sysRestart" divided>
                  <el-icon :size="15"><RefreshLeft /></el-icon>
                  重启
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-tabs v-model="activeIndex" type="card" @tab-remove="removeTab" @tab-click="clickTab">
        <el-tab-pane v-for="item in openTab" :key="item.name" :label="item.title" :name="item.name" :closable="item.closable">
          <router-view v-if="item.name === activeIndex"></router-view>
        </el-tab-pane>
      </el-tabs>
      <!--        <el-main>
          &lt;!&ndash; 路由占位符 &ndash;&gt;
          <router-view></router-view>
        </el-main>-->
    </el-container>
  </el-container>
</template>

<script>
import router from "@/router/index";
import session from "@/util/session";

export default {
  data() {
    return {
      // 左侧菜单数据对象：仪盘表
      meterList: [],
      // 左侧菜单数据对象：服务管理
      serviceManageList: [],
      // 左侧菜单数据对象：用户管理
      userManageList: [],
      // 系统状态
      systemSettings: [],
      // 是否折叠
      isCollapse: false,
      parentName: "",
      // 面包屑的菜单
      breakList: "",
      activeIndex: "/system",
      openTab: [
        {
          title: "首页",
          name: "/system",
          closable: false,
        },
      ],
      userInfo: {}, // 用户信息
    };
  },
  created() {
    this.getCurrentUserInfo();
    this.getMenuList();
    this.getBreakCrumb();
    this.$router.push({ path: this.activeIndex }); // 刷新整个页面时，openTab恢复默认值，需要跳到默认页
  },
  watch: {
    $route(to, from) {
      if (to.path !== "/login") {
        this.getBreakCrumb();
        let flag = false;
        // 当前页面菜单已打开
        for (let i = 0; i < this.openTab.length; i++) {
          if (to.path === this.openTab[i].name) {
            this.activeIndex = this.openTab[i].name;
            flag = true;
            break;
          }
        }
        // 打开新的页面
        if (!flag) {
          const obj = {
            title: to.meta.title,
            name: to.path,
            closable: true,
          };
          this.activeIndex = to.path;
          this.openTab.push(obj);
        }
      }
    },
  },
  methods: {
    logout() {
      window.sessionStorage.clear();
      this.$router.push("/login");
    },
    // 从页面路由中获取所有的菜单数据
    async getMenuList() {
      const routes = router.getRoutes();
      const userLevel = this.userInfo.level;
      routes.forEach((item) => {
        if (!item.meta.hidden && item.meta.level >= userLevel) {
          if (item.meta.parent === "meter") {
            this.meterList.push(item);
          }
          if (item.meta.parent === "ServiceManage") {
            this.serviceManageList.push(item); // 服务管理的子菜单
          }
          if (item.meta.parent === "UserManage") {
            this.userManageList.push(item);
          }
          if (item.meta.parent === "SystemSettings") {
            this.systemSettings.push(item);
          }
        }
      });

      // 成功了，进行赋值
      // this.menulist = res.data;
      // console.log(res);
    },
    // 点击按钮，切换菜单的折叠与展开
    toggleCollapse() {
      this.isCollapse = !this.isCollapse;
    },

    /**
     * 获取点击跳转的路由.
     */
    getBreakCrumb() {
      this.breakList = this.$route.matched.filter((item) => item.meta.title && item.meta.parent);
      switch (this.breakList[0].meta.parent) {
        case "meter":
          this.parentName = "仪盘表";
          break;
        case "ServiceManage":
          this.parentName = "服务管理";
          break;
        case "UserManage":
          this.parentName = "用户管理";
          break;
        case "SystemSettings":
          this.parentName = "系统设置";
          break;
        default:
          this.parentName = "平台管理";
      }
    },

    /**
     * 点击标签页.
     * @param tab 点中的标签页.
     */
    clickTab(tab) {
      this.activeIndex = tab.paneName;
      this.$router.push({ path: this.activeIndex });
    },

    /**
     * 关闭标签页.
     * @param target 目标页.
     */
    removeTab(target) {
      // 删除的是当前选中的页面
      if (this.activeIndex === target) {
        this.openTab.forEach((item, index) => {
          if (item.name === target) {
            const nextTab = item[index + 1] || item[index - 1];
            if (nextTab) {
              this.activeIndex = nextTab.name;
            }
          }
        });
      }
      let i = 0;
      this.openTab.forEach((item, index) => {
        if (item.name === target) {
          return (i = index);
        }
      });
      this.openTab.splice(i, 1);

      // 更新路由
      this.$router.push({ path: this.openTab[this.openTab.length - 1].name });
    },

    getCurrentUserInfo() {
      if (session.getUserInfo()) {
        this.userInfo = JSON.parse(session.getUserInfo());
        switch (this.userInfo.role) {
          case "admin":
            this.userInfo.level = 1;
            break;
          case "editor":
            this.userInfo.level = 2;
            break;
          default:
            this.userInfo.level = 3;
        }
      }
    },

    /**
     * 系统重启.
     */
    sysRestart() {
      ElMessageBox.confirm("确认后设备将会重启，是否继续?", "重启", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
      }).then(() => {
        this.afterLoading(3, "系统将重启", 1000, this.$SERVICE_PLATFORM.serverApi.systemRestart);
      });
    },

    /**
     * 系统关机.
     */
    sysShutdown() {
      ElMessageBox.confirm("确认后设备将会关机，是否继续?", "关机", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
      }).then(() => {
        this.afterLoading(3, "系统将关机", 1000, this.$SERVICE_PLATFORM.serverApi.systemShutdown);
      });
    },

    /**
     * 创建加载页面，并在倒计时结束后执行对应的请求.
     * @param {int} time 倒计时.
     * @param {string} hint 提示信息.
     * @param {int} timeout 定时器间隔时间.
     * @param {string} url 请求的url.
     */
    afterLoading(time, hint, timeout, url) {
      if (time > 0) {
        const loading = ElLoading.service({
          lock: true,
          text: `倒计时${time}秒后${hint}`,
          background: "rgba(0, 0, 0, 0.7)",
        });
        const timer = setInterval(() => {
          if (time === 0) {
            clearInterval(timer);
            this.$http
              .post(url)
              .then(() => {
                this.logout();
              })
              .catch((error) => {
                ElMessage.error(error.toString());
              });
            loading.close();
          } else {
            loading.setText(`倒计时${time}秒后${hint}`);
            time -= 1;
          }
        }, timeout);
        return timer;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
