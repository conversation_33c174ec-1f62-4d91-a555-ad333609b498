.aside-button {
  // 添加背景颜色
  background-color: #545c64;
  // 设置文本大小
  font-size: 20px;
  //设置字体加粗
  font-weight: bolder;
  // 设置文本行高
  line-height: 40px;
  // 设置文本颜色
  color: #f08519;
  // 设置文本居中
  text-align: center;
  .coryLogo {
    width: 80%;
  }
  .corpLogoMini {
    width: 50%;
  }
}
.common-layout {
  height: 100%;
}
.el-header {
  background-color: #363d40;
  // 给头部设置一下弹性布局
  display: flex;
  // 让它贴标左右对齐
  justify-content: space-between;
  // 清空图片左侧padding
  padding-left: 0;
  // 按钮居中
  align-items: center;
  // 文本颜色
  color: #fff;
  // 嵌套
  > div {
    width: 50%;
    // 弹性布局
    display: flex;
    // 纵向上居中对齐
    align-items: center;
    // 给文本和图片添加间距，使用类选择器
  }
  .toggle-button {
    padding-bottom: 10px;
  }
}
.el-aside {
  background-color: #313743;
}
.el-main {
  background-color: #e9edf1;
}
.toggle-button {
  // 设置文本大小
  font-size: 22px;
  // 设置文本间距
  letter-spacing: 0.2em;
  padding: 10px 0 0 20px;
  // 设置鼠标悬浮变小手效果
  cursor: pointer;
}
.el-icon {
  margin-right: 20px;
}

.headerBread {
  color: white;
}

// 面包屑字体
::v-deep(.el-breadcrumb__item) {
  .el-breadcrumb__inner {
    color: #ffffff;
  }
}

:deep(.el-tabs__nav-wrap .el-tabs__nav-scroll) {
  .el-tabs__nav {
    border: none;
    .el-tabs__item {
      color: #cccccc;
      border: none;
    }
    .el-tabs__item.is-active {
      color: #f08519;
      font-weight: bold;
      border-bottom: 2px solid #f08519;
    }
  }
}

.dropdown-button {
  cursor: pointer;
  color: white;
}
