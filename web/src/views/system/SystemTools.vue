<template>
  <div>
    <div class="app-container">
      <div class="filter-container">
        <el-card>
          <el-button @click="handleShowNetworkDetectionTool" type="primary">网络检测工具</el-button>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue";
import { networkDetectionTool } from "../../util/networkDetectionTool.jsx";

export default defineComponent({
  name: "SystemTools",
  methods: {
    handleShowNetworkDetectionTool() {
      networkDetectionTool().showDialog();
    },
  },
});
</script>
