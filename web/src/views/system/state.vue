<template>
  <div v-loading="loadingVis">
    <el-row :gutter="15" class="system_state">
      <el-col :span="12">
        <el-card v-if="runningState.cpu" class="card_item">
          <template #header>
            <div>CPU</div>
          </template>
          <div>
            <el-row :gutter="10">
              <el-col :span="12">CPU 核数:</el-col>
              <el-col :span="12" v-text="runningState.cpu.cores" />
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">CPU 利用率:</el-col>
              <el-col :span="12">
                <el-progress
                  type="line"
                  :percentage="runningState.cpu.used "
                  :color="colors"
                />
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card v-if="runningState.disk" class="card_item">
          <template #header>
            <div>磁盘</div>
          </template>
          <div>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-row :gutter="10">
                  <el-col :span="12">磁盘大小 (MB)</el-col>
                  <el-col :span="12" v-text="runningState.disk.total" />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">已使用 (MB)</el-col>
                  <el-col :span="12" v-text="runningState.disk.used" />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">磁盘大小 (GB)</el-col>
                  <el-col
                      :span="12"
                      v-text="(runningState.disk.total / 1024).toFixed(2)"
                  />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">已使用 (GB)</el-col>
                  <el-col
                      :span="12"
                      v-text="(runningState.disk.used / 1024).toFixed(2)"
                  />
                </el-row>
              </el-col>
              <el-col :span="12">
                <el-progress
                    type="dashboard"
                    :percentage="Math.round((this.runningState.disk.used / this.runningState.disk.total) * 100)"
                />
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="15" class="system_state">
      <el-col :span="12">
        <el-card
            v-if="runningState.network"
            class="card_item"
        >
          <template #header>
            <div style="display: inline">
              网络
            </div>
            <div style="float: right">
              <el-button type="primary" @click="editIp">设置网络</el-button>
            </div>
          </template>

          <div>
            <el-table
                    :data="runningState.network"
                    :default-sort="{prop: 'name', order: 'descending'}"
                    style="width: 100%" height="200"
                    border stripe
            >
              <el-table-column prop="name" label="网卡名" sortable/>
              <el-table-column prop="ip" label="IP" />
              <el-table-column prop="connected" label="状态" >
                <template v-slot="scope">
                  <el-tooltip content="网线已连接" placement="top" v-if="scope.row.connected">
                    <el-icon color="green"><Connection /></el-icon>
                  </el-tooltip>
                  <el-tooltip content="网线未连接" placement="top" v-else>
                    <el-icon><RemoveFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card v-if="runningState.ram" class="card_item">
          <template #header>
            <div>内存</div>
          </template>
          <div>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-row :gutter="10">
                  <el-col :span="12">内存大小 (MB)</el-col>
                  <el-col :span="12" v-text="runningState.ram.total" />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">已使用 (MB)</el-col>
                  <el-col :span="12" v-text="runningState.ram.used" />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">内存大小 (GB)</el-col>
                  <el-col
                      :span="12"
                      v-text="(runningState.ram.total / 1024).toFixed(2)"
                  />
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">已使用 (GB)</el-col>
                  <el-col
                      :span="12"
                      v-text="(runningState.ram.used / 1024).toFixed(2)"
                  />
                </el-row>
              </el-col>
              <el-col :span="12">
                <el-progress
                    type="dashboard"
                    :percentage="Math.round((this.runningState.ram.used / this.runningState.ram.total) * 100)"
                    :color="colors"
                />
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>

  <el-dialog
      v-model="editMasterNetworkVis"
      title="修改网卡"
      width="40%"
      draggable
  >
    <el-collapse>
      <el-collapse-item :title="net.name" v-for="(net, index) in masterNetForm" :key="index">
        <el-form label-width="90px" :model="net" ref="masterNetFormRef" :rules="rules">
          <el-form-item label="Dhcp" prop="dhcp">
            <el-select v-model="net.dhcp" placeholder="请设置Dhcp" @change="dhcpSwitch($event, net)">
              <el-option
                  v-for="item in dhcp4Model"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              >
              </el-option>
            </el-select>
            <el-tooltip content="添加默认网关" placement="top">
              <el-button type="primary" size="default" @click="addGateway(net)" style="width: 35px" link>
                <el-icon><plus /></el-icon>
              </el-button>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="地址" prop="address" v-if="!net.dhcp">
            <el-col :span="10">
              <el-input v-model="net.address" placeholder="请输入IP地址">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="掩码" prop="netmask" v-if="!net.dhcp">
            <el-col :span="10">
              <el-input v-model="net.netmask" placeholder="请输入掩码地址">
              </el-input>
            </el-col>
          </el-form-item>
            <el-form-item label="网关" prop="gateway" v-if="!net.dhcp && (net.gateway || net.gateway === '')">
              <el-col :span="10">
                <el-input v-model="net.gateway" placeholder="请输入网关地址">
                </el-input>
              </el-col>
              <el-button size="small" link @click="delete net.gateway"><el-icon><delete /></el-icon></el-button>
            </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="editNetSubmit">提交</el-button>
        <el-button @click="editMasterNetworkVis = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getSystemInfo } from '../../util/doRequest';
import { onUnmounted } from 'vue';

export default {
  data() {
    let checkName = (rule, value, callback) => {
      const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
      if (!value) {
        return callback(new Error('不能为空'));
      }
      if (value && !reg.test(value)) {
        return callback(new Error('请输入正确的地址格式,如 *************'));
      }
      callback();
    };
    return {
      loadingVis: true,
      editMasterNetworkVis: false,
      masterNetForm: [
        // {
        //   name: 'test',
        //   dhcp: 'false',
        //   address: '',
        //   netmask: '',
        //   gateway: '',
        // },
      ],

      dhcp4Model: [
        { label: '启动', value: true },
        { label: '关闭', value: false },
      ],
      masterNetName: '',
      colors: [
        { color: '#5cb87a', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#f56c6c', percentage: 80 }],
      timer: '',
      runningState: {}, // 系统运行状态
      rules: {
        address: [
          {
            required: true, trigger: true, message: '不能为空',
          },
          {
            validator: checkName,
          },
        ],
        netmask: [
          {
            required: true, trigger: true, message: '不能为空',
          },
          {
            validator: checkName,
          },
        ],
        gateway: [
          {
            required: true, trigger: true, message: '不能为空',
          },
          {
            validator: checkName,
          },
        ],
      },
    };
  },
  created() {
    getSystemInfo(this.$http, (data, error) => {
      if (!error) {
        this.runningState = data;
        if (this.loadingVis) {
          this.loadingVis = false;
        }
      }
    });
    this.timer = setInterval(() => {
      getSystemInfo(this.$http, (data, error) => {
        if (!error) {
          this.runningState = data;
        }
      });
    }, 5000);
    onUnmounted(() => {
      clearInterval(this.timer);
    });
  },
  methods: {
    async editIp() {
      this.editMasterNetworkVis = true;
      await this.$http.get(this.$SERVICE_PLATFORM.serverApi.networkCards).then((res) => {
        this.masterNetForm = res.data;
      }).catch((error) => {
        ElMessage.error('获取网卡信息失败');
      });
    },
    async editNetSubmit() {
      let validateRes = true;
      let verifyNum = 0;
      this.$refs.masterNetFormRef.forEach((netFormRef) => {
        netFormRef.validate((valid, fields) => {
          if (fields) {
            validateRes = false;
          }
          verifyNum += 1;
          if (validateRes && verifyNum === this.$refs.masterNetFormRef.length) {
            let count = 0;
            for (let i = 0; i < this.masterNetForm.length; i += 1) {
              if (this.masterNetForm[i].gateway) {
                count += 1;
              }
            }
            if (count >= 2) {
              ElMessage.error('系统仅能存在一个网关');
              return;
            }
            console.log('_______________');
            this.$http.post(this.$SERVICE_PLATFORM.serverApi.setSystemNet, this.masterNetForm).then(() => {
              ElMessage.success('修改宿主机网卡成功');
            }).catch((error) => {
              ElMessage.error('修改宿主机网卡成功');
              console.error(error);
            });
            this.editMasterNetworkVis = false;
          }
        });
      });
    },

    dhcpSwitch(event, net) {
      if (event) {
        ElMessage.warning('谨慎启用Dhcp功能！启用后将由路由器动态分配IP！');
        delete net.address;
        delete net.netmask;
        delete net.gateway;
      }
    },

    addGateway(net) {
      if (net.dhcp || net.gateway) {
        return;
      }
      for (let i = 0; i < this.masterNetForm.length; i ++) {
        if (this.masterNetForm[i].gateway || this.masterNetForm[i].gateway === '') {
          ElMessage.warning('系统仅能设置一个网关');
          return;
        }
      }
      net.gateway = '';
    },
  },
};
</script>

<style>
.system_state {
  padding: 10px;
}

.card_item {
  height: 280px;
}
</style>
