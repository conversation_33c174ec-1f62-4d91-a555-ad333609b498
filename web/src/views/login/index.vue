<template>
  <div id="userLayout">
    <div class="login_panle" :style="{ backgroundImage: `url(${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.pictures.loginBackground.url})` }">
      <div class="login_panle_form">
        <div class="login_panle_form_title">
          <img
              class="login_panle_form_title_logo"
              :src="`${this.$SERVICE_PLATFORM.serverBaseURL}${this.$SERVICE_PLATFORM.pictures.loginLogo.url}`"
              alt
          >
        </div>
        <div class="login_panle_form_title">
          <p class="login_panle_form_title_p">{{ $SERVICE_PLATFORM.appName }}</p>
        </div>
        <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="rules"
            @keyup.enter="submitForm"
        >
          <el-form-item prop="username">
            <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
            >
              <template #suffix>
                <span class="input-icon">
                  <el-icon>
                    <user />
                  </el-icon>
                </span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
                v-model="loginForm.password"
                :type="lock === 'lock' ? 'password' : 'text'"
                placeholder="请输入密码"
            >
              <template #suffix>
                <span class="input-icon">
                  <el-icon>
                    <component
                        :is="lock"
                        @click="changeLock"
                    />
                  </el-icon>
                </span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button
                type="primary"
                size="large"
                style="width: 46%; background-color: #f08519"
                @click="clearForm"
            >重 置</el-button>
            <el-button
                type="primary"
                size="large"
                style="width: 46%; margin-left: 30px; background-color: #f08519"
                @click="submitForm">
              登 录</el-button>
          </el-form-item>
        </el-form>
        <span v-text="version" style="margin-left: 42%; color: #777777"></span>
      </div>
      <div class="login_panle_right"></div>
    </div>
  </div>
</template>

<script>
import { getSoftInfo, getUserInfo } from '../../util/doRequest';
import session from '../../util/session';

export default {
  data() {
    return {
      version: '', // 版本号
      loginForm: {
        username: '',
        password: '',
      },
      // 表单规则验证
      rules: {
        username: [
          { required: true, message: '请输入登录名称', trigger: 'blur' },
          {
            min: 5, max: 12, message: '长度在 5 到 12 个字符', trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入登录密码', trigger: 'blur' },
          {
            min: 5, max: 12, message: '长度在 5 到 12 个字符', trigger: 'blur',
          },
        ],
      },
      lock: 'lock',
    };
  },
  created() {
    this.getVersion();
  },
  methods: {
    changeLock() {
      this.lock = this.lock === 'lock' ? 'unlock' : 'lock';
    },

    submitForm() {
      this.$refs.loginFormRef.validate(async (v) => {
        if (v) {
          await this.$http.post(this.$SERVICE_PLATFORM.serverApi.loginServer, this.loginForm).then((res) => {
            ElMessage.success('登录成功');
            session.setTokenAndExpire(res.data.token, res.data.expire);
            getUserInfo(this.$http, this.loginForm.username, (data, err) => {
              if (!err) {
                session.setUserInfo(JSON.stringify(data));
                this.$router.push('/system');
              } else {
                console.error(err);
              }
            });
          }).catch((error) => {
            console.error(error);
            ElMessage.error('登录失败，账号或密码错误');
          });
        } else {
          ElMessage({
            type: 'error',
            message: '请正确填写登录信息',
            showClose: true,
          });
        }
      });
    },

    clearForm() {
      this.$refs.loginFormRef.resetFields();
    },

    getVersion() {
      getSoftInfo(this.$http, (res, error) => {
        if (error) {
          console.log('version', error);
          return;
        }
        this.version = res.data.version;
      });
    },
  },
};

</script>

<style lang="scss" scoped>
@import "@/style/newLogin.scss";
</style>
