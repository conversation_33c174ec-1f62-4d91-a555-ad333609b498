<template>
  <div class="app-container">
    <div class="filter-container">
      <!-- 卡片视图区域 -->
      <el-card>
        <template #header>
          <div class="header">
            <el-button type="primary" @click="createAccountVis = true">创建</el-button>
            <el-button type="danger" @click="delUser">删除</el-button>
            <el-button type="primary" @click="getUsers">刷新数据</el-button>
          </div>
        </template>
        <!-- 用户列表区域  -->
        <el-table :data="userList" height="600" style="width: 100%" border stripe @selection-change="userSelectionChange">
          <el-table-column type="index"></el-table-column>
          <el-table-column fixed type="selection" width="50"></el-table-column>
          <el-table-column label="账号" prop="username"></el-table-column>
          <el-table-column label="角色" prop="role">
            <template v-slot="scope">
              <el-select v-model="scope.row.role" placeholder="请选择" @change="editRole(scope.row)" filterable allow-create v-if="scope.row.role !== 'admin'">
                <el-option v-for="item in roleOption " :key="item.role" :label="item.role" :value="item.role">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createdAt"></el-table-column>
          <el-table-column label="最近一次修改" prop="updatedAt"></el-table-column>
          <el-table-column label="描述" prop="comment">
            <template v-slot="scope">
              <el-input v-model="scope.row.comment" placeholder="输入描述内容" @change="editComment(scope.row)">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180px">
            <template v-slot="scope">
              <el-row :gutter="5">
                <el-space wrap>
                  <!-- 修改按钮 -->
                  <el-col :span="2">
                    <el-tooltip effect="dark" content="修改密码" placement="top" :enterable="false">
                      <el-button type="primary" size="small" @click="handleEdit(scope.row)"><el-icon><edit /></el-icon></el-button>
                    </el-tooltip>
                  </el-col>
                  <!-- 删除按钮 -->
                  <el-col :span="2" v-if="scope.row.username !== 'admin'">
                    <el-popconfirm title="此操作将永久删除, 是否继续?" @confirm="handleDel(scope.row.username)">
                      <template #reference>
                        <div>
                          <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                            <el-button type="danger" size="small"><el-icon><delete /></el-icon></el-button>
                          </el-tooltip>
                        </div>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-space>
              </el-row>
            </template>
          </el-table-column>
        </el-table>

        <!-- 新建账号表单 -->
        <el-dialog
            v-model="createAccountVis"
            title="注册账号"
            width="30%"
            draggable
        >
          <el-form label-width="80px" :model="accountForm" :rules="createAccountRule" ref="accountFormRef">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="accountForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input placeholder="请输入密码" v-model="accountForm.password" show-password></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="rePassword">
              <el-input placeholder="请输入确认密码" v-model="accountForm.rePassword" show-password></el-input>
            </el-form-item>
            <el-form-item label="角色" prop="role">
              <el-select v-model="accountForm.role" placeholder="请选择角色">
                <el-option label="管理员" value="editor"></el-option>
                <el-option label="普通用户" value="readonly"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="描述信息">
              <el-input v-model="accountForm.comment"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="createAccount">提交</el-button>
                <el-button @click="createAccountVis = false">取消</el-button>
              </span>
          </template>
        </el-dialog>

        <!-- 修改密码表单 -->
        <el-dialog
          v-model="editPasswordVis"
          title="修改密码"
          width="30%"
          draggable
        >
          <el-form label-width="80px" :model="accountEditForm" :rules="createAccountRule" ref="passwordFormRef">
            <el-form-item label="新密码" prop="password">
              <el-input placeholder="请输入密码" v-model="accountEditForm.password" show-password></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="rePassword">
              <el-input placeholder="请输入确认密码" v-model="accountEditForm.rePassword" show-password></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="submitPasswordEdit">提交</el-button>
                <el-button @click="editPasswordVis = false">取消</el-button>
              </span>
          </template>
        </el-dialog>
      </el-card>

    </div>
  </div>
  <div class="app-container">
    <div class="filter-container">

    </div>
  </div>
</template>

<script>
import { formatDate } from '../../util/transferFormat';

export default {
  name: 'userManage',
  data() {
    const checkName = (rule, value, callback) => {
      const reg = /^[a-zA-Z0-9_]{0,}$/;

      if (!value) {
        return callback(new Error('不能为空'));
      }
      if (!reg.test(value)) {
        return callback(new Error('请输入由英文字母、下划线及数字组成的字符串'));
      }
      if (value.length < 5 || value.length > 12) {
        return callback(new Error('长度在 5 到 12 个字符'));
      }
      callback();
    };

    return {
      createAccountVis: false,
      editPasswordVis: false,
      // 用户创建表单
      accountForm: {
        username: '',
        password: '',
        rePassword: '',
        role: '',
        comment: '',
      },
      // 用户修改表单
      accountEditForm: {
        username: '',
        password: '',
      },
      createAccountRule: {
        username: [
          { required: true, validator: checkName, trigger: 'blur' },
        ],
        password: [
          { required: true, validator: checkName, trigger: 'blur' },
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'blur' },
        ],
        rePassword: [
          { required: true, validator: checkName, trigger: 'blur' },
        ],
      },
      userSelection: '',
      userList: [],
      user: '',
      resetOn: {
        resetBtn: true,
      },
      roleOption: [
        { role: 'editor' },
        { role: 'readonly' },
      ],
    };
  },
  created() {
    this.getUsers();
  },
  methods: {
    async getUsers() {
      this.userList = [];
      this.$http.get(this.$SERVICE_PLATFORM.serverApi.users).then((res) => {
        res.data.forEach((user) => {
          this.userList.push({
            username: user.Username,
            role: user.Role,
            createdAt: formatDate(user.CreatedAt),
            updatedAt: formatDate(user.UpdatedAt),
            comment: user.Comment,
          });
        });
      }).catch((error) => {
        ElMessage.error(error.toString());
      });
    },

    createAccount() {
      this.$refs.accountFormRef.validate((val) => {
        if (val) {
          if (this.accountForm.password !== this.accountForm.rePassword) {
            ElMessage.error('两次输入的密码不一致');
            return;
          }
          let exit = false;
          this.userList.forEach((currentUser) => {
            if (currentUser.username === this.accountForm.username) {
              exit = true;
            }
          });
          if (exit) {
            ElMessage.error('用户已存在');
            return;
          }
          this.$http.post(this.$SERVICE_PLATFORM.serverApi.userRegister, this.accountForm).then(() => {
            ElMessage.success('创建账号成功');
            this.createAccountVis = false;
            this.getUsers();
          }).catch((error) => {
            ElMessage.error('创建账号失败');
            console.error(error);
          });
        }
      });
    },

    /**
     * 提交修改密码表单.
     * @returns {Promise<void>}
     */
    async submitPasswordEdit() {
      this.$refs.passwordFormRef.validate((val) => {
        if (val) {
          if (this.accountEditForm.password !== this.accountEditForm.rePassword) {
            ElMessage.error('两次输入的密码不一致');
            return;
          }
          this.accountEditForm.username = this.user.username;
          this.$http.post(this.$SERVICE_PLATFORM.serverApi.updateUserInfo, this.accountEditForm).then(() => {
            ElMessage.success('修改密码成功');
            this.editPasswordVis = false;
            this.getUsers();
          }).catch((error) => {
            ElMessage.error('修改密码失败');
            console.error(error);
          });
        }
      });
    },

    /**
     * 修改按钮点击事件处理.
     * @param row
     */
    handleEdit(row) {
      this.editPasswordVis = true;
      this.user = row;
    },

    /**
     * 删除按钮点击事件处理.
     * @param name 用户名称.
     */
    handleDel(name) {
      this.$http.delete(this.$SERVICE_PLATFORM.serverApi.userDel, {
        data: {
          username: name,
        },
      }).then(() => {
        this.getUsers();
      }).catch((error) => {
        console.error(error);
      });
    },

    /**
     * 删除用户.
     */
    delUser() {
      if (this.userSelection.length <= 0) {
        ElMessage.info('请先选中账号');
        return;
      }
      ElMessageBox.confirm('此操作将永久删除该账号, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.userSelection.forEach((select) => {
          if (select.username !== 'admin') {
            this.$http.delete(this.$SERVICE_PLATFORM.serverApi.userDel, {
              data: {
                username: select.username,
              },
            }).then(() => {
              this.getUsers();
            }).catch((error) => {
              console.error(error);
            });
          } else {
            ElMessage.info('禁止删除初始管理员账号');
          }
        });
      }).catch(() => {
        ElMessage.info('取消删除');
      });
    },

    userSelectionChange(val) {
      this.userSelection = val;
    },

    /**
     * 编辑用户描述信息.
     * @param row {}.
     * @returns {Promise<void>}
     */
    async editComment(row) {
      ElMessageBox.confirm('此操作将修改该用户的描述信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
         this.$http.post(this.$SERVICE_PLATFORM.serverApi.updateDes, {
          username: row.username,
          comment: row.comment,
        }).then(() => {
          this.getUsers();
        }).catch((error) => {
          console.error('修改用户信息失败', error);
          ElMessage.info('修改用户信息失败');
        });
      }).catch(() => {
        this.getUsers();
      });
    },

    /**
     * 编辑用户角色.
     * @param row {}.
     * @returns {Promise<void>}
     */
    async editRole(row) {
      ElMessageBox.confirm('此操作将修改该用户的角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$http.post(this.$SERVICE_PLATFORM.serverApi.updateRole, {
          username: row.username,
          role: row.role,
          comment: row.comment,
        }).then(() => {
          this.getUsers();
        }).catch((error) => {
          console.error('修改用户信息失败', error);
          ElMessage.info('修改用户信息失败');
        });
      }).catch(() => {
        this.getUsers();
      });
    },

  },
};
</script>

<style scoped>

</style>
