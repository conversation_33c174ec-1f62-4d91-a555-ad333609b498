import Status from "@/views/DeviceStatus/menuEntry";
import System from "@/views/DeviceStatus/systemEntry";
import Container from "@/views/docker/container";
import Image from "@/views/docker/image";
import Network from "@/views/docker/network";
import Storage from "@/views/docker/storage";
import Home from "@/views/home/<USER>";
import Login from "@/views/login/index";
import State from "@/views/system/state";
import SystemTools from "@/views/system/SystemTools.vue";
import AccountManage from "@/views/user/accountManage";

import * as vueRouter from "vue-router";

/**
 * constantRoutes
 * 页面路由
 */
export const constantRoutes = [
  {
    path: "/login",
    component: Login,
    meta: {
      hidden: true,
    },
  },
  {
    path: "/",
    redirect: "/login",
    meta: {
      hidden: true,
    },
  },
  {
    path: "/home",
    component: Home,
    redirect: "/status",
    children: [
      // role level [admin: 1, editor: 2，readonly: 3]
      // 服务管理
      {
        path: "/container",
        component: Container,
        name: "ContainerManage",
        meta: {
          parent: "ServiceManage",
          title: "容器管理",
          icon: "monitor",
          hidden: false,
          level: 2,
        },
      },
      {
        path: "/image",
        component: Image,
        name: "ImageManage",
        meta: {
          parent: "ServiceManage",
          title: "镜像管理",
          icon: "compass",
          hidden: false,
          level: 2,
        },
      },
      {
        path: "/network",
        component: Network,
        name: "NetworkManage",
        meta: {
          parent: "ServiceManage",
          title: "网络管理",
          icon: "connection",
          hidden: false,
          level: 2,
        },
      },
      {
        path: "/storage",
        component: Storage,
        name: "StorageManage",
        meta: {
          parent: "ServiceManage",
          title: "存储管理",
          icon: "notebook",
          hidden: false,
          level: 2,
        },
      },
      // 用户管理
      {
        path: "/accountManage",
        component: AccountManage,
        name: "AccountManage",
        meta: {
          parent: "UserManage",
          title: "账号管理",
          icon: "stamp",
          hidden: false,
          level: 1,
        },
      },
      // 仪盘表
      {
        path: "/system",
        component: System,
        name: "SystemEntry",
        meta: {
          parent: "meter",
          title: "服务入口",
          icon: "position",
          hidden: false,
          level: 3,
        },
      },
      {
        path: "/status",
        component: Status,
        name: "DeviceStatus",
        meta: {
          parent: "meter",
          title: "系统查询",
          icon: "homeFilled",
          hidden: false,
          level: 2,
        },
      },
      // 服务器设置
      {
        path: "/state",
        component: State,
        name: "SystemState",
        meta: {
          parent: "SystemSettings",
          title: "服务器状态",
          icon: "cloudy",
          hidden: false,
          level: 1,
        },
      },
      // 服务器设置
      {
        path: "/system-tools",
        component: SystemTools,
        name: "SystemTools",
        meta: {
          parent: "SystemSettings",
          title: "系统工具",
          icon: "tools",
          hidden: false,
          level: 1,
        },
      },
    ],
    meta: {
      hidden: true,
    },
  },
];

const router = vueRouter.createRouter({
  history: vueRouter.createWebHistory(),
  routes: constantRoutes,
});

// 路由导航守卫
router.beforeEach((to, form, next) => {
  if (to.path === "/login" || to.path === "/") return next();
  const token = window.sessionStorage.getItem("token");
  if (!token) return next("/login");
  next();
});

export default router;
