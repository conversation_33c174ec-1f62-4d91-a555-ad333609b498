# 封装在一个函数或脚本中，通过 nsenter 执行
# nsenter --target 1 --all bash -c '
detect_network_tool() {
    if command -v nmcli >/dev/null && systemctl is-active NetworkManager &>/dev/null; then
        echo "NetworkManager"
        return
    fi

    if command -v networkctl >/dev/null && systemctl is-active systemd-networkd &>/dev/null; then
        echo "systemd-networkd"
        return
    fi

    if command -v netplan >/dev/null && [ -d /etc/netplan ]; then
        # Netplan 本身是前端，后端可能是 NetworkManager 或 systemd-networkd
        # 需要进一步判断 netplan 的 renderer
        # 但操作通常通过 netplan 命令进行
        echo "Netplan"
        return
    fi

    # 检查经典的 ifupdown (Debian/Ubuntu 风格)
    if [ -f /etc/network/interfaces ] && command -v ifup >/dev/null && command -v ifdown >/dev/null; then
        # 进一步确认是否是主要的管理方式
        # 例如，检查 /etc/network/interfaces 是否包含实际的接口配置，而不仅仅是 "source /etc/network/interfaces.d/*"
        if grep -qE "^\s*iface\s+\w+\s+inet" /etc/network/interfaces; then
             echo "ifupdown_debian"
             return
        fi
    fi

    # 检查经典的 network-scripts (RHEL/CentOS 风格)
    # 注意：即使 NetworkManager 存在，这些脚本也可能存在并被 NM 读取
    # 但如果没有 NM，这些脚本配合 network.service 就是主要方式
    if [ -d /etc/sysconfig/network-scripts ] && ls /etc/sysconfig/network-scripts/ifcfg-* &>/dev/null; then
        if ! systemctl is-active NetworkManager &>/dev/null; then # 如果NM没在运行
             echo "ifupdown_rhel" # 指的是 network.service 和 ifup/ifdown 脚本
             return
        fi
    fi

    echo "unknown_or_iproute2_only" # 可能只用 ip 命令手动配置，或者无法判断
}

DETECTED_TOOL=$(detect_network_tool)
echo "Detected host network tool: $DETECTED_TOOL"
# '