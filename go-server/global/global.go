package global

import (
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	GvaDb      *gorm.DB
	GvaLog     *zap.SugaredLogger
	AppVersion string
)

const (
	ImagePath       string = "images"
	ImagePngType    string = "png"
	ImageIcoType    string = "ico"
	CompanyLogo     string = "corpLogo"
	CompanyLogoMini string = "corpLogoMini"
	LoginLogo       string = "loginLogo"
	LoginBackground string = "loginBg"
	FAVICON         string = "favicon"
	AesKey          string = "cteQeL7mZE2HUgIHbrVB35Fx2gRhNUH4"
	ComposeConf     string = "docker-compose.yaml"
	ProjectLabel    string = "com.docker.compose.project"
	LogPath         string = "LOG_PATH"
	ComposeBin      string = "/usr/local/bin/docker-compose"
)
