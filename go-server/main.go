package main

import (
	"fmt"
	"os"

	"github.com/spf13/viper"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/initialize"
	router "mediacomm.com/skylink/router/docker"
)

var VERSION string

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("service-platform version: %s\n", VERSION)
		return
	}
	global.AppVersion = VERSION
	initialize.Viper()  // 初始化Viper
	initialize.Logger() // 初始化日志
	defer global.GvaLog.Sync()
	initialize.Gorm() // gorm连接数据库
	if global.GvaDb != nil {
		initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GvaDb.DB()
		defer db.Close()
	}
	//initialize.InitTestData()

	global.GvaLog.Info("Server start")
	global.GvaLog.Info("service-platform version: ", VERSION)

	r := router.NewRouter()

	port := viper.GetString("server.port")
	if port != "" {
		global.GvaLog.Info("Listening on " + port)
		panic(r.Run(":" + port))
	}
	global.GvaLog.Info("Listening on 8080")
	panic(r.Run()) // listen and serve on 0.0.0.0:8080
}
