package initialize

import (
	"os"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"mediacomm.com/skylink/global"
)

func Logger() {
	var logInfoPath string
	var logErrorPath string
	if gin.Mode() == gin.ReleaseMode {
		logInfoPath = "/data/log/info.log"
		logErrorPath = "/data/log/error.log"
	} else {
		logInfoPath = "info.log"
		logErrorPath = "error.log"
	}
	var coreArr []zapcore.Core                        //日志文件存放目录
	encoderConfig := zap.NewProductionEncoderConfig() //指定时间格式
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoder := zapcore.NewConsoleEncoder(encoderConfig) //获取编码器,NewJSONEncoder()输出json格式，NewConsoleEncoder()输出普通文本格式

	//日志级别
	highPriority := zap.LevelEnablerFunc(func(lev zapcore.Level) bool { //error级别
		return lev >= zap.ErrorLevel
	})
	lowPriority := zap.LevelEnablerFunc(func(lev zapcore.Level) bool { //info和debug级别,debug级别是最低的
		return lev < zap.ErrorLevel && lev >= zap.InfoLevel
	})

	//info文件writeSyncer
	infoFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename:   logInfoPath, //日志文件存放目录，如果文件夹不存在会自动创建
		MaxSize:    5,           //文件大小限制,单位MB
		MaxBackups: 100,         //最大保留日志文件数量
		MaxAge:     30,          //日志文件保留天数
		Compress:   false,       //是否压缩处理
	})
	//error文件writeSyncer
	//第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志
	infoFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(infoFileWriteSyncer, zapcore.AddSync(os.Stdout)), lowPriority)
	errorFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename:   logErrorPath, //日志文件存放目录
		MaxSize:    5,            //文件大小限制,单位MB
		MaxBackups: 20,           //最大保留日志文件数量
		MaxAge:     30,           //日志文件保留天数
		Compress:   false,        //是否压缩处理
	})

	//第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志
	errorFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(errorFileWriteSyncer, zapcore.AddSync(os.Stdout)), highPriority)
	coreArr = append(coreArr, infoFileCore)
	coreArr = append(coreArr, errorFileCore)

	debug := true // TODO : 读取启动命令中的"-debug"
	if debug {
		debugPriority := zap.LevelEnablerFunc(func(lev zapcore.Level) bool { //info和debug级别,debug级别是最低的
			return lev == zap.DebugLevel
		})
		debugCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(os.Stdout), debugPriority)
		coreArr = append(coreArr, debugCore)
	}

	logger := zap.New(zapcore.NewTee(coreArr...), zap.AddCaller()) //zap.AddCaller()为显示文件名和行号，可省略
	global.GvaLog = logger.Sugar()

}
