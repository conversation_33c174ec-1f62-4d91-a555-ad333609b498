package initialize

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/glebarez/sqlite"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/model/system"
)

func Gorm() {
	var dbPath string
	if gin.Mode() == gin.ReleaseMode {
		dbPath = "/data/gorm.sqlite"
	} else {
		dbPath = "gorm.sqlite"
	}
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		global.GvaLog.Error("failed to connect database")
		global.GvaDb = nil
		return
	}
	global.GvaDb = db
}

// RegisterTables 注册数据库表专用
func RegisterTables() {
	err := global.GvaDb.AutoMigrate(
		system.Auth{},
	)
	if err != nil {
		global.GvaLog.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}
	global.GvaLog.Info("register table success")
}

func InitTestData() {
	// admin := &system.Auth{Username: "admin", Password: utils.MD5V([]byte("123456"))}
	admin := &system.Auth{Username: "admin", Password: "MediaC0mm", Role: "admin", Comment: "admin user"}
	global.GvaDb.Create(&admin)
}
