package utils

import (
	"fmt"
	"sort"
	"strings"
	"testing"

	"mediacomm.com/skylink/model/system"
)

func TestGenerateFixedIP(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行，因为需要访问/host/sys
	t.Skip("Skipping TestGenerateFixedIP - requires container environment with /host/sys mounted")
}

// TestGenerateFixedIPLogic 测试固定IP生成的逻辑
func TestGenerateFixedIPLogic(t *testing.T) {
	// 模拟网卡列表
	mockInterfaces := []string{"enaphyt4i1", "enaphyt4i0", "eth0", "eth1"}

	tests := []struct {
		name          string
		interfaceName string
		interfaces    []string
		wantIP        string
		wantErr       bool
	}{
		{
			name:          "First interface after sorting",
			interfaceName: "enaphyt4i0",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 0 + 10 = 10
			wantErr:       false,
		},
		{
			name:          "Second interface after sorting",
			interfaceName: "enaphyt4i1",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 1 + 10 = 11
			wantErr:       false,
		},
		{
			name:          "Third interface after sorting",
			interfaceName: "eth0",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 2 + 10 = 12
			wantErr:       false,
		},
		{
			name:          "Fourth interface after sorting",
			interfaceName: "eth1",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 3 + 10 = 13
			wantErr:       false,
		},
		{
			name:          "Non-existent interface",
			interfaceName: "nonexistent",
			interfaces:    mockInterfaces,
			wantIP:        "",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟generateFixedIP的逻辑
			interfaces := make([]string, len(tt.interfaces))
			copy(interfaces, tt.interfaces)

			// 对网卡名进行字符排序
			sort.Strings(interfaces)

			// 找到当前网卡在排序后的位置
			var position int = -1
			for i, iface := range interfaces {
				if iface == tt.interfaceName {
					position = i
					break
				}
			}

			var got string
			var err error
			if position == -1 {
				err = fmt.Errorf("interface %s not found in physical interfaces", tt.interfaceName)
			} else {
				thirdOctet := position + 10
				got = fmt.Sprintf("10.10.%d.10/24", thirdOctet)
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("generateFixedIP logic error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.wantIP {
				t.Errorf("generateFixedIP logic = %v, want %v", got, tt.wantIP)
			}
			if !tt.wantErr {
				t.Logf("Generated fixed IP for %s: %s (sorted interfaces: %v)", tt.interfaceName, got, interfaces)
			}
		})
	}
}

func TestDetectNetworkManager(t *testing.T) {
	manager, err := DetectNetworkManager()
	if err != nil {
		t.Logf("DetectNetworkManager() error = %v", err)
	} else {
		t.Logf("Detected network manager: %s", manager)
	}
}

func TestApplyHostNetworkConfig(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行
	t.Skip("Skipping integration test - requires container environment with nsenter")

	configs := []system.NetworkInterface{
		{
			Name:    "enaphyt4i0",
			DHCP:    false,
			Address: "*************",
			Netmask: "*************",
			Gateway: "***********",
		},
	}

	err := ApplyHostNetworkConfig(configs)
	if err != nil {
		t.Errorf("ApplyHostNetworkConfig() error = %v", err)
	}
}

func TestNetmaskToCIDR(t *testing.T) {
	tests := []struct {
		name    string
		mask    string
		want    int
		wantErr bool
	}{
		{
			name:    "Valid /24 netmask",
			mask:    "*************",
			want:    24,
			wantErr: false,
		},
		{
			name:    "Valid /16 netmask",
			mask:    "***********",
			want:    16,
			wantErr: false,
		},
		{
			name:    "Valid /8 netmask",
			mask:    "*********",
			want:    8,
			wantErr: false,
		},
		{
			name:    "Invalid netmask",
			mask:    "invalid",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := netmaskToCIDR(tt.mask)
			if (err != nil) != tt.wantErr {
				t.Errorf("netmaskToCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("netmaskToCIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestApplyWithIfupdownDebian(t *testing.T) {
	// 这个测试主要验证配置文件内容的生成逻辑，不会实际执行 nsenter 命令
	t.Log("Testing ifupdown configuration generation logic...")

	// 测试数据
	configs := map[string][]system.NetworkInterface{
		"eth0": {
			{
				Name:    "eth0",
				DHCP:    false,
				Address: "*************",
				Netmask: "*************",
				Gateway: "***********",
			},
		},
		"eth1": {
			{
				Name:    "eth1",
				DHCP:    true,
				Address: "",
				Netmask: "",
				Gateway: "",
			},
		},
		"eth2": {
			{
				Name:    "eth2",
				DHCP:    false,
				Address: "********00",
				Netmask: "*************",
				Gateway: "",
			},
			{
				Name:    "eth2",
				DHCP:    false,
				Address: "**********",
				Netmask: "*************",
				Gateway: "********",
			},
		},
	}

	// 测试静态IP配置生成
	testIfupdownStaticConfig(t, configs["eth0"])

	// 测试DHCP配置生成
	testIfupdownDHCPConfig(t, configs["eth1"])

	// 测试多IP配置生成
	testIfupdownMultiIPConfig(t, configs["eth2"])
}

func testIfupdownStaticConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing static IP configuration generation...")

	// 模拟 applyWithIfupdownDebian 中的配置生成逻辑
	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if !configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))

		// 添加用户配置的IP地址
		aliasIndex := 0
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				if aliasIndex == 0 {
					// 第一个用户IP作为主IP
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				} else {
					// 其他IP作为别名接口
					configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				}
				aliasIndex++
			}
		}

		// 添加网关（以最后一个非空的网关为准）
		var gateway string
		for _, config := range configs {
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}
		if gateway != "" {
			configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
		}

		configContent.WriteString("    dns-nameservers ******* ***************\n")
	}

	result := configContent.String()
	t.Logf("Generated static config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth0 inet static") {
		t.Error("Static configuration should contain 'iface eth0 inet static'")
	}
	if !strings.Contains(result, "address *************") {
		t.Error("Static configuration should contain correct IP address")
	}
	if !strings.Contains(result, "netmask *************") {
		t.Error("Static configuration should contain correct netmask")
	}
	if !strings.Contains(result, "gateway ***********") {
		t.Error("Static configuration should contain correct gateway")
	}
	if !strings.Contains(result, "dns-nameservers ******* ***************") {
		t.Error("Static configuration should contain DNS nameservers")
	}
}

func testIfupdownDHCPConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing DHCP configuration generation...")

	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet dhcp\n", iface))
	}

	result := configContent.String()
	t.Logf("Generated DHCP config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth1 inet dhcp") {
		t.Error("DHCP configuration should contain 'iface eth1 inet dhcp'")
	}
}

func testIfupdownMultiIPConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing multi-IP configuration generation...")

	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if !configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))

		// 添加用户配置的IP地址（作为别名）
		aliasIndex := 0
		var gateway string
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				if aliasIndex == 0 {
					// 第一个IP作为主IP
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				} else {
					// 其他IP作为别名接口
					configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				}
				aliasIndex++
			}
			// 以最后一个非空的网关为准
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}

		if gateway != "" {
			configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
		}
		configContent.WriteString("    dns-nameservers ******* ***************\n")
	}

	result := configContent.String()
	t.Logf("Generated multi-IP config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth2 inet static") {
		t.Error("Multi-IP configuration should contain 'iface eth2 inet static'")
	}
	if !strings.Contains(result, "address ********00") {
		t.Error("Multi-IP configuration should contain first IP address")
	}
	if !strings.Contains(result, "iface eth2:1 inet static") {
		t.Error("Multi-IP configuration should contain alias interface")
	}
	if !strings.Contains(result, "address **********") {
		t.Error("Multi-IP configuration should contain second IP address")
	}
	if !strings.Contains(result, "gateway ********") {
		t.Error("Multi-IP configuration should contain gateway")
	}
}
