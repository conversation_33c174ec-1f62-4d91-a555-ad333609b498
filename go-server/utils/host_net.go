package utils

import (
	"bytes"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/model/system"
)

const (
	// HostSysPath 是容器内映射的宿主机 /sys 路径
	HostSysPath = "/host/sys"
	// NetworkManager Tools
	ToolNetworkManager  = "NetworkManager"
	ToolSystemdNetworkd = "systemd-networkd"
	ToolIfupdownDebian  = "ifupdown_debian"
	ToolUnknown         = "unknown"
)

// runOnHost 使用 nsenter 在宿主机命名空间中执行命令
func runOnHost(args ...string) ([]byte, error) {
	cmdArgs := append([]string{"--target", "1", "--all"}, args...)
	cmd := exec.Command("nsenter", cmdArgs...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("failed to run command '%s' on host: %w\nstdout: %s\nstderr: %s",
			strings.Join(args, " "), err, stdout.String(), stderr.String())
	}
	return stdout.Bytes(), nil
}

// 1. 获取物理网络接口
func GetPhysicalInterfaces() ([]string, error) {
	var interfaces []string
	// 从挂载的宿主机 /sys 目录读取
	netPath := filepath.Join(HostSysPath, "class/net")
	entries, err := os.ReadDir(netPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read host net dir %s: %w. Ensure host /sys is mounted to /host/sys", netPath, err)
	}
	for _, entry := range entries {
		// 检查是否是虚拟接口
		linkPath := filepath.Join(netPath, entry.Name())
		realPath, err := os.Readlink(linkPath)
		if err != nil {
			// 如果无法读取链接，也暂时跳过
			continue
		}
		if !strings.Contains(realPath, "/virtual/") {
			interfaces = append(interfaces, entry.Name())
		}
	}
	return interfaces, nil
}

// 2. 判断网络管理工具
func DetectNetworkManager() (string, error) {
	// 检查 NetworkManager
	_, err := runOnHost("systemctl", "is-active", "NetworkManager")
	if err == nil {
		fmt.Println("Detected NetworkManager is active.")
		return ToolNetworkManager, nil
	}
	// 检查 systemd-networkd
	_, err = runOnHost("systemctl", "is-active", "systemd-networkd")
	if err == nil {
		fmt.Println("Detected systemd-networkd is active.")
		return ToolSystemdNetworkd, nil
	}
	// 检查传统的 ifupdown (Debian 风格)
	output, err := runOnHost("cat", "/etc/network/interfaces")
	if err == nil && strings.Contains(string(output), "iface") {
		fmt.Println("Detected ifupdown (Debian style).")
		return ToolIfupdownDebian, nil
	}
	fmt.Println("Could not detect a known network manager, will fallback to 'ip' command for temporary changes.")
	return ToolUnknown, nil
}

// 3. 根据网络管理工具修改网络配置
func ApplyHostNetworkConfig(configs []system.NetworkInterface) error {
	physicalInterfaces, err := GetPhysicalInterfaces()
	if err != nil {
		return fmt.Errorf("could not get physical interfaces: %w", err)
	}
	fmt.Printf("Found physical interfaces: %v\n", physicalInterfaces)
	// 验证输入配置中的网卡名是否为物理网卡
	for _, config := range configs {
		found := slices.Contains(physicalInterfaces, config.Name)
		if !found {
			return fmt.Errorf("interface %s from config is not a valid physical interface", config.Name)
		}
	}
	// 检测网络管理工具
	manager, err := DetectNetworkManager()
	if err != nil {
		return fmt.Errorf("could not detect network manager: %w", err)
	}
	// 按网卡名对配置进行分组，以支持单网卡多IP
	configsByInterface := make(map[string][]system.NetworkInterface)
	for _, config := range configs {
		configsByInterface[config.Name] = append(configsByInterface[config.Name], config)
	}
	// 应用配置
	switch manager {
	case ToolNetworkManager:
		return applyWithNetworkManager(configsByInterface)
	case ToolSystemdNetworkd:
		fmt.Println("Applying with systemd-networkd is not yet implemented.")
		// TODO: 实现 systemd-networkd 的配置逻辑
		// 1. 生成 .network 文件到宿主机的 /etc/systemd/network/
		// 2. runOnHost("systemctl", "restart", "systemd-networkd")
		return applyWithSystemdNetworkd(configsByInterface)
	case ToolIfupdownDebian:
		fmt.Println("Applying with ifupdown is not yet implemented.")
		return applyWithIfupdownDebian(configsByInterface)
	default:
		fmt.Println("Warning: Falling back to temporary 'ip' command configuration.")
		return applyWithIPRoute2(configsByInterface)
	}
}

// applyWithNetworkManager 使用 nmcli 命令进行持久化配置
func applyWithNetworkManager(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 检查该设备是否已存在连接
		out, err := runOnHost("nmcli", "-g", "NAME,DEVICE", "connection", "show")
		if err != nil {
			return fmt.Errorf("failed to list nmcli connections: %w", err)
		}
		// 查找设备对应的连接名
		var connName string
		lines := strings.SplitSeq(strings.TrimSpace(string(out)), "\n")
		for line := range lines {
			parts := strings.Split(line, ":")
			if len(parts) == 2 && parts[1] == iface {
				connName = parts[0]
				break
			}
		}
		// 确定是修改还是添加
		var cmdArgs []string
		if connName != "" {
			// 修改现有连接
			fmt.Printf("Modifying existing connection '%s' for device %s\n", connName, iface)
			cmdArgs = []string{"connection", "modify", connName}
		} else {
			// 添加新连接
			fmt.Printf("Adding new connection for device %s\n", iface)
			connName = fmt.Sprintf("conn-%s", iface)
			cmdArgs = []string{"connection", "add", "type", "ethernet", "con-name", connName, "ifname", iface}
		}
		// 处理 DHCP 或静态 IP
		// 注意: 一个连接只能是 DHCP 或静态，这里以列表中的第一个配置为准
		if configs[0].DHCP {
			cmdArgs = append(cmdArgs, "ipv4.method", "auto")
		} else {
			cmdArgs = append(cmdArgs, "ipv4.method", "manual")
			var addresses []string
			var gateway string
			// 添加固定IP地址 (10.10.x.10/24)
			fixedIP, err := generateFixedIP(iface)
			if err != nil {
				fmt.Printf("Warning: failed to generate fixed IP for %s: %v\n", iface, err)
			} else {
				addresses = append(addresses, fixedIP)
				fmt.Printf("Added fixed IP %s for interface %s\n", fixedIP, iface)
			}
			// 添加用户配置的IP地址
			for _, config := range configs {
				if config.Address != "" && config.Netmask != "" {
					prefix, err := netmaskToCIDR(config.Netmask)
					if err != nil {
						return fmt.Errorf("invalid netmask %s: %w", config.Netmask, err)
					}
					userIP := fmt.Sprintf("%s/%d", config.Address, prefix)
					addresses = append(addresses, userIP)
					fmt.Printf("Added user IP %s for interface %s\n", userIP, iface)
				}
				// 以最后一个非空的网关为准
				if config.Gateway != "" {
					gateway = config.Gateway
				}
			}
			if len(addresses) > 0 {
				cmdArgs = append(cmdArgs, "ipv4.addresses", strings.Join(addresses, ","))
			}
			if gateway != "" {
				cmdArgs = append(cmdArgs, "ipv4.gateway", gateway)
			}
			// 可选：添加 DNS
			cmdArgs = append(cmdArgs, "ipv4.dns", "*******,***************")
		}
		// 执行修改/添加命令
		if _, err := runOnHost(append([]string{"nmcli"}, cmdArgs...)...); err != nil {
			return err
		}
		// 重新激活连接以应用更改
		fmt.Printf("Re-activating connection '%s'...\n", connName)
		if _, err := runOnHost("nmcli", "connection", "down", connName); err != nil {
			// down 失败可能是因为它本来就 down，可以忽略特定错误，但这里为简单起见直接返回
			fmt.Printf("Warning: failed to bring down connection '%s', might be normal: %v\n", connName, err)
		}
		if _, err := runOnHost("nmcli", "connection", "up", connName); err != nil {
			return fmt.Errorf("failed to bring up connection '%s': %w", connName, err)
		}
	}
	return nil
}

// applyWithSystemdNetworkd 使用 systemd-networkd 进行持久化配置
func applyWithSystemdNetworkd(configsByInterface map[string][]system.NetworkInterface) error {
	return nil
}

// applyWithIfupdownDebian 使用传统的 ifupdown 进行持久化配置
func applyWithIfupdownDebian(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 生成接口配置文件路径
		configPath := fmt.Sprintf("/etc/network/interfaces.d/%s", iface)
		// 构建配置内容
		var configContent strings.Builder
		configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
		configContent.WriteString(fmt.Sprintf("auto %s\n", iface))
		// 处理 DHCP 或静态 IP
		// 注意: 一个接口只能是 DHCP 或静态，这里以列表中的第一个配置为准
		if configs[0].DHCP {
			configContent.WriteString(fmt.Sprintf("iface %s inet dhcp\n", iface))
		} else {
			// 静态IP配置
			configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))
			// 添加用户配置的IP地址
			aliasIndex := 0
			for _, config := range configs {
				if config.Address != "" && config.Netmask != "" {
					if aliasIndex == 0 {
						// 第一个用户IP作为主IP
						configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
						configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
					} else {
						// 其他IP作为别名接口
						configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
						configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
						configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
						configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
					}
					aliasIndex++
					fmt.Printf("Added user IP %s/%s for interface %s\n", config.Address, config.Netmask, iface)
				}
			}
			// 添加网关（以最后一个非空的网关为准）
			var gateway string
			for _, config := range configs {
				if config.Gateway != "" {
					gateway = config.Gateway
				}
			}
			if gateway != "" {
				configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
			}
			// 添加DNS
			configContent.WriteString("    dns-nameservers ******* ***************\n")
		}
		// 使用 nsenter 和 tee 将配置写入宿主机文件
		fmt.Printf("Writing configuration to %s\n", configPath)
		configData := configContent.String()
		// 使用 tee 命令写入文件（覆盖模式）
		cmd := fmt.Sprintf("echo '%s' | tee %s > /dev/null", configData, configPath)
		if _, err := runOnHost("sh", "-c", cmd); err != nil {
			return fmt.Errorf("failed to write config to %s: %w", configPath, err)
		}
		// 重启网络接口
		fmt.Printf("Restarting interface %s\n", iface)
		if _, err := runOnHost("sh", "-c", fmt.Sprintf("ifdown %s && ifup %s", iface, iface)); err != nil {
			return fmt.Errorf("failed to restart interface %s: %w", iface, err)
		}
	}
	return nil
}

// applyWithIPRoute2 使用 ip 命令进行临时配置 (非持久化)
func applyWithIPRoute2(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 清理接口上所有现有IP
		fmt.Printf("Flushing IP addresses on %s\n", iface)
		if _, err := runOnHost("ip", "addr", "flush", "dev", iface); err != nil {
			return err
		}

		var gateway string
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				prefix, err := netmaskToCIDR(config.Netmask)
				if err != nil {
					return err
				}
				fmt.Printf("Adding IP %s/%d to %s\n", config.Address, prefix, iface)
				if _, err := runOnHost("ip", "addr", "add", fmt.Sprintf("%s/%d", config.Address, prefix), "dev", iface); err != nil {
					return err
				}
			}
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}

		// 添加默认路由 (需要先确保接口是 up 状态)
		if _, err := runOnHost("ip", "link", "set", "dev", iface, "up"); err != nil {
			return err
		}
		if gateway != "" {
			fmt.Printf("Adding default gateway %s\n", gateway)
			if _, err := runOnHost("ip", "route", "add", "default", "via", gateway, "dev", iface); err != nil {
				// 如果路由已存在，会报错，可以尝试先删除
				if _, delErr := runOnHost("ip", "route", "del", "default"); delErr == nil {
					if _, addErr := runOnHost("ip", "route", "add", "default", "via", gateway, "dev", iface); addErr != nil {
						return addErr
					}
				} else {
					return err
				}
			}
		}
	}
	return nil
}

// netmaskToCIDR 将子网掩码转换为 CIDR 前缀长度
func netmaskToCIDR(mask string) (int, error) {
	ipMask := net.ParseIP(mask)
	if ipMask == nil {
		return 0, fmt.Errorf("invalid netmask format")
	}
	ipv4Mask := ipMask.To4()
	if ipv4Mask == nil {
		return 0, fmt.Errorf("not an IPv4 netmask")
	}
	prefix, _ := net.IPv4Mask(ipv4Mask[0], ipv4Mask[1], ipv4Mask[2], ipv4Mask[3]).Size()
	return prefix, nil
}

// generateFixedIP 根据网卡名字符排序生成固定IP地址
// 格式: 10.10.x.10/24，其中x根据网卡名在所有物理网卡中的字符排序位置决定
func generateFixedIP(interfaceName string) (string, error) {
	// 获取所有物理网卡
	physicalInterfaces, err := GetPhysicalInterfaces()
	if err != nil {
		return "", fmt.Errorf("failed to get physical interfaces: %w", err)
	}
	// 对网卡名进行字符排序
	sort.Strings(physicalInterfaces)
	// 找到当前网卡在排序后的位置
	var position int = -1
	for i, iface := range physicalInterfaces {
		if iface == interfaceName {
			position = i
			break
		}
	}
	if position == -1 {
		return "", fmt.Errorf("interface %s not found in physical interfaces", interfaceName)
	}
	// 生成IP地址: 10.10.x.10/24，x从10开始（position + 10）
	thirdOctet := position + 10
	if thirdOctet > 255 {
		return "", fmt.Errorf("too many interfaces, cannot generate valid IP for position %d", position)
	}
	fixedIP := fmt.Sprintf("10.10.%d.10/24", thirdOctet)
	return fixedIP, nil
}

func ChangeHostNetwork(c *gin.Context) {
	// 提取请求体
	var configs []system.NetworkInterface
	if err := c.ShouldBindJSON(&configs); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
}
