package utils

import (
	cryptobin "github.com/deatil/go-cryptobin/cryptobin/crypto"
	"mediacomm.com/skylink/global"
)

func AesEncryptECB(data string) string {
	encrypted := cryptobin.
		FromString(data).
		SetKey(global.AesKey).
		Aes().
		ECB().
		PKCS7Padding().
		Encrypt().
		ToBase64String()
	return encrypted
}

func AesDecryptECB(data string) string {
	decrypted := cryptobin.
		FromBase64String(data).
		SetKey(global.AesKey).
		Aes().
		ECB().
		PKCS7Padding().
		Decrypt().
		ToString()
	return decrypted
}
