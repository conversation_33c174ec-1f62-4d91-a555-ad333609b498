package utils

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os/exec"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

func Copy(to, from interface{}) error {
	b, err := json.Marshal(from)
	if err != nil {
		return fmt.Errorf("序列化失败: %w", err)
	}

	err = json.Unmarshal(b, to)
	if err != nil {
		return fmt.Errorf("反序列化失败: %w", err)
	}

	return nil
}

func IsRelease() bool {
	return gin.Mode() == gin.ReleaseMode
}

func BeautifyStruct(s interface{}) string {
	b, _ := json.MarshalIndent(s, "", " ")
	return string(b)
}

// CheckImageType 检查图片格式
// filename - 图片文件名
// imgType - 图片格式
func CheckImageType(filename string, imgType string) bool {
	split := strings.Split(filename, ".")
	suffix := split[len(split)-1]
	suffix = strings.ToLower(suffix)
	return suffix == imgType
}

// isValidDirname 检查文件夹名是否合法
func IsValidDirname(dirname string) bool {
	// 文件夹名不能为空
	if len(dirname) == 0 {
		return false
	}

	// 文件夹名不能包含 '/' 或者空字符 '\0'
	invalidChars := regexp.MustCompile(`[\/\0]`)
	if invalidChars.MatchString(dirname) {
		return false
	}

	// 可选：检查是否包含其他特殊字符 (例如 `?`, `*`, `|`, `<`, `>`, `:`, `"`, `\`)
	specialChars := regexp.MustCompile(`[<>:"\\|?*]`)
	return !specialChars.MatchString(dirname)
}

// 实时 tcping 指定的主机并逐步返回结果
func Tcping(conn *websocket.Conn, messageType int, host string, port string) error {
	req := fmt.Sprintf("tcping %s %s", host, port)
	cmd  := exec.Command(req)

	// 获取输出流
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return err
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		return err
	}

	// 创建逐行读取器
	scanner := bufio.NewScanner(stdout)
	for scanner.Scan() {
		line := scanner.Text()
		fmt.Printf("ping 输出: %s\n", line)

		// 实时将每一行发送给客户端
		if err := conn.WriteMessage(messageType, []byte(line)); err != nil {
			return err
		}
	}

	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		return err
	}

	return nil
}
