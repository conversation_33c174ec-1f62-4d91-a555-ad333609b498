package controller

import (
	"net/http"
	"strconv"

	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/volume"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
)

// VolumeCreate - Create a volume
// @Summary     创建挂载卷
// @Schemes     http
// @Description 创建挂载卷
// @Tags        volume
// @Produce     json
// @Success     200 {object} model.Volume "Create a volume"
// @Router      /v1//volumes/create [post]
func VolumeCreate(c *gin.Context) {
	var opt volume.CreateOptions
	if err := c.ShouldBindJ<PERSON>N(&opt); err != nil {
		global.GvaLog.Error("[VolumeCreate] Parsing json " + err.Error())
		c.J<PERSON>(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	res, err := getClient().VolumeCreate(ctx, opt)
	if err != nil {
		global.GvaLog.Errorf("[VolumeCreate] create %s", err.Error())
		c.<PERSON>(http.StatusBadRequest, gin.H{"message": err.<PERSON>r()})
		return
	}
	c.JSON(http.StatusCreated, res)
}

// VolumeDelete - Remove a volume
// @Summary     删除挂载卷
// @Schemes     http
// @Description 删除挂载卷
// @Tags        volume
// @Produce     json
// @Router      /v1/volumes/{name} [delete]
func VolumeDelete(c *gin.Context) {
	name := c.Param("name")
	force, err := strconv.ParseBool(c.Query("force"))
	if err != nil {
		force = false
	}
	err = getClient().VolumeRemove(ctx, name, force)
	if err != nil {
		global.GvaLog.Errorf("Volume Delete %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// VolumeInspect - Inspect a volume
// @Summary     挂载卷信息
// @Schemes     http
// @Description 挂载卷信息
// @Tags        volume
// @Produce     json
// @Success     200 {object} model.Volume "Inspect a volume"
// @Router      /v1/volumes/{name} [get]
func VolumeInspect(c *gin.Context) {
	volumeID := c.Param("name")
	res, err := getClient().VolumeInspect(ctx, volumeID)
	if err != nil {
		global.GvaLog.Errorf("Volume Inspect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

// VolumeList - List volumes
// @Summary     获取所有挂载卷列表
// @Schemes     http
// @Description 挂载卷列表
// @Tags        volume
// @Produce     json
// @Success     200 {object} model.VolumeListResponse "Volume List"
// @Router      /v1/volumes [get]
func VolumeList(c *gin.Context) {
	volumelist, err := getClient().VolumeList(ctx, volume.ListOptions{})
	if err != nil {
		global.GvaLog.Errorf("Volume List %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, volumelist)
}

// VolumePrune - Delete unused volumes
// @Summary     删除未使用的卷
// @Schemes     http
// @Description 删除未使用的卷
// @Tags        volume
// @Produce     json
// @Success     200 {object} model.VolumePruneResponse "Delete unused volumes"
// @Router      /v1/volumes/prune [post]
func VolumePrune(c *gin.Context) {
	res, err := getClient().VolumesPrune(ctx, filters.Args{})
	if err != nil {
		global.GvaLog.Errorf("Volume Prune %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}
