package controller

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/duke-git/lancet/v2/validator"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	ping "github.com/prometheus-community/pro-bing"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/utils/tcping"
)

type PingHandler func(ctx context.Context) *tcping.Stats

func (ph PingHandler) Ping(ctx context.Context) *tcping.Stats {
	return ph(ctx)
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有跨域请求
	},
}

var (
	mu       sync.Mutex
	pinger   *ping.Pinger
	tcpinger *tcping.Pinger
)

// 处理 WebSocket 连接
func GpingHandle(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		global.GvaLog.Error("升级到 WebSocket 失败:", err)
		return
	}
	defer func() {
		stopGping()
		conn.Close()
	}()

	for {
		// 读取消息
		messageType, msg, err := conn.ReadMessage()
		if err != nil {
			// 读取消息失败，可能是客户端断开连接, 停止当前 ping 任务
			global.GvaLog.Error("读取消息失败:", err)
			break
		}

		command := string(msg)

		if command == "stop" {
			stopGping() // 停止当前 gping 任务
			continue
		}

		// 执行 gping
		startGping(conn, messageType, command)
	}
}

func startGping(conn *websocket.Conn, messageType int, host string) {
	hostArr := strings.Split(host, ":")
	if len(hostArr) == 1 {
		if !validator.IsIpV4(hostArr[0]) {
			response := fmt.Sprintf("Parameter is not an ipv4 address, %s", hostArr[0])
			conn.WriteMessage(messageType, []byte(response))
			return
		}
		// 执行 ping
		err := startPing(conn, messageType, host)
		if err != nil {
			response := fmt.Sprintf("ping %s 失败: %s", host, err)
			conn.WriteMessage(messageType, []byte(response))
		}
	} else if len(hostArr) == 2 {
		if !validator.IsIpV4(hostArr[0]) {
			response := fmt.Sprintf("Parameter is not an ipv4 address, %s", hostArr[0])
			conn.WriteMessage(messageType, []byte(response))
			return
		}
		if !validator.IsNumberStr(hostArr[1]) {
			response := fmt.Sprintf("Illegal port, %s", hostArr[1])
			conn.WriteMessage(messageType, []byte(response))
			return
		}
		// 执行 tcping
		err := startTcping(conn, messageType, hostArr[0], hostArr[1])
		if err != nil {
			response := fmt.Sprintf("ping %s 失败: %s", host, err)
			conn.WriteMessage(messageType, []byte(response))
		}
	} else {
		response := fmt.Sprintf("invalid host %s format", host)
		conn.WriteMessage(messageType, []byte(response))
	}
}

func startTcping(conn *websocket.Conn, messageType int, host string, port string) error {
	// 如果有正在执行的 tcping 任务，先停止它
	stopTcping()
	mu.Lock()
	defer mu.Unlock()
	u, _ := url.Parse(fmt.Sprintf("tcp://%s:%s", host, port))
	tcpinger = tcping.NewPinger(conn, messageType, u, PingHandler(func(ctx context.Context) *tcping.Stats {
		return &tcping.Stats{
			Address:   host + ":" + port,
			Connected: true,
			Duration:  time.Second,
		}
	}), time.Second, 0)
	go tcpinger.Ping()
	return nil
}

// 启动 ping 任务
func startPing(conn *websocket.Conn, messageType int, host string) error {
	// 如果有正在执行的 ping 任务，先停止它
	stopPing()
	mu.Lock()
	defer mu.Unlock()

	var err error
	pinger, err = ping.NewPinger(host)
	if err != nil {
		global.GvaLog.Error("new ping error:", err)
		return err
	}
	// 配置 pinger 参数
	pinger.SetPrivileged(true) // 设置为特权模式，以便非 root 用户运行
	// 设置处理收到 ICMP 响应的函数
	pinger.OnRecv = func(pkt *ping.Packet) {
		line := fmt.Sprintf("%d bytes from %s: icmp_seq=%d time=%v", pkt.Nbytes, pkt.IPAddr, pkt.Seq, pkt.Rtt)

		// 实时将每一行发送给客户端
		if err := conn.WriteMessage(messageType, []byte(line)); err != nil {
			stopPing() // 如果发送失败则停止 ping
		}
	}

	// 设置 ping 结束后的处理函数
	pinger.OnFinish = func(stats *ping.Statistics) {
		line := fmt.Sprintf("\n--- %s ping statistics ---\n%d packets transmitted, %d packets received, %v%% packet loss, round-trip min/avg/max/stddev = %v/%v/%v/%v\n",
			stats.Addr, stats.PacketsSent, stats.PacketsRecv, stats.PacketLoss, stats.MinRtt, stats.AvgRtt, stats.MaxRtt, stats.StdDevRtt)
		conn.WriteMessage(messageType, []byte(line))
	}
	// 设置最大 ping 次数，或者你可以删除它让它一直运行
	// pinger.Count = 10
	// 异步启动 ping
	go func() {
		if err := pinger.Run(); err != nil {
			global.GvaLog.Error("ping 失败:", err)
		}
	}()

	return nil
}
// 停止 gping 任务
func stopGping() {
	mu.Lock()
	defer mu.Unlock()
	if pinger != nil {
		pinger.Stop() // 停止当前 pinger
		pinger = nil
	}
	if tcpinger != nil {
		tcpinger.Stop() // 停止当前 tcpinger
		tcpinger.Summarize()
		tcpinger = nil
	}
}

// 停止 ping 任务
func stopPing() {
	mu.Lock()
	defer mu.Unlock()
	if pinger != nil {
		pinger.Stop() // 停止当前 pinger
		pinger = nil
	}
}

// 停止 tcping 任务
func stopTcping() {
	mu.Lock()
	defer mu.Unlock()
	if tcpinger != nil {
		tcpinger.Stop() // 停止当前 tcpinger
		tcpinger = nil
	}
}

