package controller

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"net/http"
	"os"
	"os/exec"
	"strings"

	"github.com/duke-git/lancet/v2/fileutil"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
	model "mediacomm.com/skylink/model/docker"
	"mediacomm.com/skylink/utils"
)

var missing_conf_msg string = "This stack was created outside of this platform. Control over this stack is limited."

// StackConf - Get stack configuration
// @Summary     获取服务配置信息
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/config [get]
func StackConf(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if stackName == "" {
		global.GvaLog.Errorln("stack name is empty")
		c.JSON(http.StatusBadRequest, gin.H{"message": "stack name is empty"})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	composeDst := stackDir + "/" + global.ComposeConf
	exist := fileutil.IsExist(composeDst)
	if !exist {
		global.GvaLog.Errorln(missing_conf_msg)
		c.JSON(http.StatusBadRequest, gin.H{"message": missing_conf_msg})
		return
	}
	fmt.Printf("composeDst: %v\n", composeDst)
	cmd := exec.Command(global.ComposeBin, "-f", composeDst, "convert", "--no-normalize", "--format", "json")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	fmt.Printf("result: %v\n", string(result))
	res := json.RawMessage(string(result))
	c.JSON(http.StatusOK, res)
}

// StackDepoly - Create and start containers
// @Summary     为服务创建容器并启动
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/up [post]
func StackDeploy(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if !utils.IsValidDirname(stackName) {
		global.GvaLog.Errorln("stack name is invalid")
		strErr := "堆栈名字不合法,不能为空,不能包含斜杠、空字符或者其他特殊字符"
		c.JSON(http.StatusBadRequest, gin.H{"message": strErr})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	if fileutil.IsExist(stackDir) {
		err := os.RemoveAll(stackDir)
		if err != nil {
			global.GvaLog.Errorln(err.Error())
		}
	}

	composeDst := stackDir + "/" + global.ComposeConf
	fmt.Printf("composeDst: %v\n", composeDst)
	err := os.MkdirAll(stackDir, fs.ModePerm)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	fileutil.CreateFile(composeDst)
	composeFile, err := c.FormFile("file")
	if err != nil {
		global.GvaLog.Errorln(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	err = c.SaveUploadedFile(composeFile, composeDst)
	if err != nil {
		global.GvaLog.Errorln(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	cmdValidate := exec.Command(global.ComposeBin, "-f", composeDst, "convert", "-q")
	validateResult, err := cmdValidate.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(validateResult))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(validateResult)})
		return
	}
	cmd := exec.Command(global.ComposeBin, "-p", stackName, "-f", composeDst, "up", "-d", "--pull", "never", "--remove-orphans")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	fmt.Printf("result: %v\n", string(result))
	c.JSON(http.StatusOK, string(result))
}

// StackStart - Start services
// @Summary     启动服务
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/start [post]
func StackStart(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if stackName == "" {
		global.GvaLog.Errorln("stack name is empty")
		c.JSON(http.StatusBadRequest, gin.H{"message": "stack name is empty"})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	composeDst := stackDir + "/" + global.ComposeConf
	exist := fileutil.IsExist(composeDst)
	if !exist {
		global.GvaLog.Errorln(missing_conf_msg)
		c.JSON(http.StatusBadRequest, gin.H{"message": missing_conf_msg})
		return
	}
	cmd := exec.Command(global.ComposeBin, "-f", composeDst, "start")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	fmt.Printf("result: %v\n", string(result))
	c.JSON(http.StatusOK, string(result))
}

// StackStop - Stop services
// @Summary     停止服务
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/stop [post]
func StackStop(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if stackName == "" {
		global.GvaLog.Errorln("stack name is empty")
		c.JSON(http.StatusBadRequest, gin.H{"message": "stack name is empty"})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	composeDst := stackDir + "/" + global.ComposeConf
	exist := fileutil.IsExist(composeDst)
	if !exist {
		global.GvaLog.Errorln(missing_conf_msg)
		c.JSON(http.StatusBadRequest, gin.H{"message": missing_conf_msg})
		return
	}
	cmd := exec.Command(global.ComposeBin, "-f", composeDst, "stop", "-t", "5")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	fmt.Printf("result: %v\n", string(result))
	c.JSON(http.StatusOK, string(result))
}

// StackRestart - Restart services
// @Summary     重启服务
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/restart [post]
func StackRestart(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if stackName == "" {
		global.GvaLog.Errorln("stack name is empty")
		c.JSON(http.StatusBadRequest, gin.H{"message": "stack name is empty"})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	composeDst := stackDir + "/" + global.ComposeConf
	exist := fileutil.IsExist(composeDst)
	if !exist {
		global.GvaLog.Errorln(missing_conf_msg)
		c.JSON(http.StatusBadRequest, gin.H{"message": missing_conf_msg})
		return
	}
	cmd := exec.Command(global.ComposeBin, "-f", composeDst, "restart", "-t", "5")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	fmt.Printf("result: %v\n", string(result))
	c.JSON(http.StatusOK, string(result))
}

// StackDown - Stop and remove services,network
// @Summary     停止并删除服务及其网络
// @Schemes     http
// @Tags        stack
// @Produce     json
// @Router      /v1/stack/down [post]
func StackDown(c *gin.Context) {
	stackName := strings.TrimSpace(c.Query("name"))
	if stackName == "" {
		global.GvaLog.Errorln("stack name is empty")
		c.JSON(http.StatusBadRequest, gin.H{"message": "stack name is empty"})
		return
	}
	pwd, _ := os.Getwd()
	stackDir := pwd + "/compose/" + stackName
	composeDst := stackDir + "/" + global.ComposeConf
	exist := fileutil.IsExist(composeDst)
	if !exist {
		global.GvaLog.Errorln(missing_conf_msg)
		c.JSON(http.StatusBadRequest, gin.H{"message": missing_conf_msg})
		return
	}
	cmd := exec.Command(global.ComposeBin, "-f", composeDst, "down", "-t", "5")
	result, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	os.RemoveAll(stackDir)
	fmt.Printf("result: %v\n", string(result))
	c.JSON(http.StatusOK, string(result))
}


// StackList - List Stack
// @Summary 获取所有堆栈列表(包括未启动)
// @Schemes http
// @Description
// @Tags    stack
// @Produce json
// @Success 200 {object} []model.Stack "Stack list"
// @Router  /v1/stack/json [get]
func StackList(c *gin.Context) {
	cmd := exec.Command(global.ComposeBin, "ls", "-a", "--format", "json")
	result, err := cmd.CombinedOutput()
	if err!= nil {
		global.GvaLog.Errorln(err.Error(), string(result))
		c.JSON(http.StatusBadRequest, gin.H{"code": err.Error(), "message": string(result)})
		return
	}
	var stackList []model.Stack
	err = json.Unmarshal([]byte(result), &stackList)
	if err!= nil {
		global.GvaLog.Errorln(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, stackList)
}
