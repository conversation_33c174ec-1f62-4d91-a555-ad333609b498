package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	model "mediacomm.com/skylink/model/docker"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/client"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// func TestT(t *testing.T) {
// 	var c = types.Container{}
// 	b, _ := json.MarshalIndent(c, "", " ")
// 	fmt.Println(string(b))
// }

// func TestPing(t *testing.T) {
// 	containersRouter.GET("/ping", func(c *gin.Context) {
// 		c.String(http.StatusOK, "pong")
// 	})
// 	w := httptest.NewRecorder()
// 	req, _ := http.NewRequest(http.MethodGet, "/v1/containers/ping", nil)
// 	engine.ServeHTTP(w, req)

// 	assert.Equal(t, http.StatusOK, w.Code)
// 	assert.Equal(t, "pong", w.Body.String())
// }

func TestContainerArchive(t *testing.T) {
	containersRouter.GET("/:id/archive", ContainerArchive)
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerArchive(tt.args.c)
		})
	}
}

func TestContainerServiceLog(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerServiceLog(tt.args.c)
		})
	}
}

func TestContainerArchiveInfo(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerArchiveInfo(tt.args.c)
		})
	}
}

func TestContainerAttach(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerAttach(tt.args.c)
		})
	}
}

func TestContainerAttachWebsocket(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerAttachWebsocket(tt.args.c)
		})
	}
}

func TestContainerChanges(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerChanges(tt.args.c)
		})
	}
}

func TestContainerCreate(t *testing.T) {
	var config model.ContainerCreateConfig
	err := json.Unmarshal([]byte(containerCreateReq), &config)
	if err != nil {
		t.Error("用例错误: ", err)
	}
}

func TestContainerDelete(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerDelete(tt.args.c)
		})
	}
}

func TestContainerExport(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerExport(tt.args.c)
		})
	}
}

func TestContainerInspect(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerInspect(tt.args.c)
		})
	}
}

func TestContainerNetwork(t *testing.T) {
	containersRouter.GET("/:id/network", ContainerNetwork)
	var target *client.Client
	containerInspectpatche := gomonkey.ApplyMethod(target, "ContainerInspect",
		func(_ *client.Client, _ context.Context, id string) (
			types.ContainerJSON, error) {
			var res types.ContainerJSON
			i, err := strconv.Atoi(id)
			if err != nil {
				t.Error("用例错误: ", err)
			}
			err = json.Unmarshal([]byte(containerInspectRes[i]), &res)
			if err != nil {
				t.Error("用例错误: ", err)
			}
			return res, nil
		})
	defer containerInspectpatche.Reset()
	for k := range containerInspectRes {
		t.Run("Test Container Network", func(_ *testing.T) {
			w := httptest.NewRecorder()
			url := fmt.Sprintf("/v1/containers/%d/network", k)
			req, _ := http.NewRequest(http.MethodGet, url, nil)
			engine.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			assert.Equal(t, containerNetworkWant[k], w.Body.String())
		})
	}

}

func TestContainerKill(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerKill(tt.args.c)
		})
	}
}

func TestContainerList(t *testing.T) {
	containersRouter.GET("/json", ContainerList)
	t.Run("Test Container List", func(_ *testing.T) {
		var target *client.Client
		containerListPatche := gomonkey.ApplyMethod(target, "ContainerList",
			func(_ *client.Client, _ context.Context, _ types.ContainerListOptions) (
				[]types.Container, error) {
				var res []types.Container
				err := json.Unmarshal([]byte(containerListRes), &res)
				if err != nil {
					t.Error("用例错误: ", err)
				}
				return res, nil
			})
		defer containerListPatche.Reset()

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/containers/json", nil)
		engine.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, containerListWant, w.Body.String())
	})
}

func Test_containerAddress(t *testing.T) {
	type args struct {
		netSetting map[string]*network.EndpointSettings
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			if got := containerAddress(tt.args.netSetting); got != tt.want {
				t.Errorf("containerAddress() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContainerLogs(t *testing.T) {
}

func TestContainerPause(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerPause(tt.args.c)
		})
	}
}

func TestContainerPrune(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerPrune(tt.args.c)
		})
	}
}

func TestContainerRename(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerRename(tt.args.c)
		})
	}
}

func TestContainerResize(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerResize(tt.args.c)
		})
	}
}

func TestContainerRestart(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerRestart(tt.args.c)
		})
	}
}

func TestContainerStart(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerStart(tt.args.c)
		})
	}
}

func TestContainerStats(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerStats(tt.args.c)
		})
	}
}

func TestContainerStop(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerStop(tt.args.c)
		})
	}
}

func TestContainerTop(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerTop(tt.args.c)
		})
	}
}

func TestContainerUnpause(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerUnpause(tt.args.c)
		})
	}
}

func TestContainerUpdate(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerUpdate(tt.args.c)
		})
	}
}

func TestContainerWait(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			ContainerWait(tt.args.c)
		})
	}
}

func TestPutContainerArchive(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			PutContainerArchive(tt.args.c)
		})
	}
}

func TestContainerEntrypoints(t *testing.T) {
	containersRouter.GET("/entrypoints", ContainerEntrypoints)
	t.Run("Test Container Entrypoints", func(_ *testing.T) {
		var target *client.Client
		containerListPatche := gomonkey.ApplyMethod(target, "ContainerList",
			func(_ *client.Client, _ context.Context, _ types.ContainerListOptions) (
				[]types.Container, error) {
				var res []types.Container
				err := json.Unmarshal([]byte(containerListRes), &res)
				if err != nil {
					t.Error("用例错误: ", err)
				}
				return res, nil
			})
		defer containerListPatche.Reset()
		containerInspectpatche := gomonkey.ApplyMethod(target, "ContainerInspect",
			func(_ *client.Client, _ context.Context, id string) (
				types.ContainerJSON, error) {
				var res types.ContainerJSON
				i, err := strconv.Atoi(id)
				if err != nil {
					t.Error("用例错误: ", err)
				}
				err = json.Unmarshal([]byte(containerInspectRes[i]), &res)
				if err != nil {
					t.Error("用例错误: ", err)
				}
				return res, nil
			})
		defer containerInspectpatche.Reset()
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/containers/entrypoints", nil)
		engine.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, containerEntrypointskWant, w.Body.String())
	})
}
