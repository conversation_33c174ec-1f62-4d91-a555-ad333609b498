package controller

import (
	"os"
	"testing"

	"github.com/gin-gonic/gin"
)

var engine *gin.Engine
var containersRouter *gin.RouterGroup

// var imagesRouter *gin.RouterGroup

func TestMain(m *testing.M) {
	gin.SetMode(gin.ReleaseMode)
	engine = gin.Default()
	groupV1 := engine.Group("v1")
	containersRouter = groupV1.Group("/containers")
	// imagesRouter := groupV1.Group("/images")
	os.Exit(m.Run())
}

var containerCreateReq = `{
	"ContainerName": "name",
	"Hostname": "",
	"Domainname": "",
	"User": "",
	"AttachStdin": false,
	"AttachStdout": false,
	"AttachStderr": false,
	"Tty": false,
	"OpenStdin": false,
	"StdinOnce": false,
	"Env": null,
	"Cmd": null,
	"Image": "image",
	"Volumes": {
		"/data": {}
	},
	"WorkingDir": "",
	"Entrypoint": null,
	"OnBuild": null,
	"Labels": null,
	"HostConfig": {
	 "Binds": [
		"volume_data:/data"
	 ],
	 "ContainerIDFile": "",
	 "LogConfig": {
	  "Type": "",
	  "Config": null
	 },
	 "NetworkMode": "",
	 "PortBindings": null,
	 "RestartPolicy": {
	  "Name": "always",
	  "MaximumRetryCount": 0
	 },
	 "AutoRemove": false,
	 "VolumeDriver": "",
	 "VolumesFrom": null,
	 "CapAdd": null,
	 "CapDrop": null,
	 "CgroupnsMode": "",
	 "Dns": null,
	 "DnsOptions": null,
	 "DnsSearch": null,
	 "ExtraHosts": null,
	 "GroupAdd": null,
	 "IpcMode": "",
	 "Cgroup": "",
	 "Links": null,
	 "OomScoreAdj": 0,
	 "PidMode": "",
	 "Privileged": true,
	 "PublishAllPorts": false,
	 "ReadonlyRootfs": false,
	 "SecurityOpt": null,
	 "UTSMode": "",
	 "UsernsMode": "",
	 "ShmSize": 0,
	 "ConsoleSize": [
	  0,
	  0
	 ],
	 "Isolation": "",
	 "CpuShares": 0,
	 "Memory": 0,
	 "NanoCpus": 0,
	 "CgroupParent": "",
	 "BlkioWeight": 0,
	 "BlkioWeightDevice": null,
	 "BlkioDeviceReadBps": null,
	 "BlkioDeviceWriteBps": null,
	 "BlkioDeviceReadIOps": null,
	 "BlkioDeviceWriteIOps": null,
	 "CpuPeriod": 0,
	 "CpuQuota": 0,
	 "CpuRealtimePeriod": 0,
	 "CpuRealtimeRuntime": 0,
	 "CpusetCpus": "",
	 "CpusetMems": "",
	 "Devices": null,
	 "DeviceCgroupRules": null,
	 "DeviceRequests": null,
	 "KernelMemory": 0,
	 "KernelMemoryTCP": 0,
	 "MemoryReservation": 0,
	 "MemorySwap": 0,
	 "MemorySwappiness": null,
	 "OomKillDisable": null,
	 "PidsLimit": null,
	 "Ulimits": null,
	 "CpuCount": 0,
	 "CpuPercent": 0,
	 "IOMaximumIOps": 0,
	 "IOMaximumBandwidth": 0,
	 "MaskedPaths": null,
	 "ReadonlyPaths": null
	},
	"NetworkingConfig": null
   }`

var containerListRes = `[{
	"Id": "0",
	"Names": ["scenic-recorder"],
	"Image": "",
	"ImageID": "",
	"Command": "",
	"Created": 0,
	"Ports": null,
	"Labels": null,
	"State": "",
	"Status": "",
	"HostConfig": {},
	"NetworkSettings": {
		"Networks": {
		  "innervlan": {
			"IPAddress": "***********"
		  }
		}
	  },
	"Mounts": null
   },
	{
	"Id": "1",
	"Names": ["jump-server"],
	"Image": "",
	"ImageID": "",
	"Command": "",
	"Created": 0,
	"Ports": null,
	"Labels": null,
	"State": "",
	"Status": "",
	"HostConfig": {},
	"NetworkSettings": {
		"Networks": {
		  "innervlan": {
			"IPAddress": "***********"
		  }
		}
	  },
	"Mounts": null
   }]`

var containerInspectRes = [...]string{`{
	"Config": {
	  "Env": [
		"Entrypoint_0_NAME=录播运维",
		"Entrypoint_0_PORT=80",
		"Entrypoint_0_PATH=/c",
		"Entrypoint_0_TAGS=服务端,录播平台"
	  ]
	},
	"NetworkSettings": {
	  "Networks": {
		"innervlan": {
		  "IPAddress": "***********"
		}
	  }
	}
  }`,
	`{
	"Config": {
	  "Env": [
		"Entrypoint_0_NAME=远端异地",
		"Entrypoint_0_PORT=80",
		"Entrypoint_0_PATH=/c",
		"Entrypoint_0_TAGS=服务端,远端异地"
	  ]
	},
	"NetworkSettings": {
	  "Networks": {
		"innervlan": {
		  "IPAddress": "***********"
		}
	  }
	}
  }`}

var containerListWant = `[{"id":"0","name":"scenic-recorder","status":"","image":"","created":"1970-01-01 08:00:00","address":"***********","restartpolicy":"","network":"","volume":""},{"id":"1","name":"jump-server","status":"","image":"","created":"1970-01-01 08:00:00","address":"***********","restartpolicy":"","network":"","volume":""}]`

var containerNetworkWant = []string{
	`[{"containerID":"0","networkID":"innervlan","ipaddress":"***********"}]`,
	`[{"containerID":"1","networkID":"innervlan","ipaddress":"***********"}]`,
}

var containerEntrypointskWant = `[{"Name":"录播运维","IP":"***********","Port":80,"Path":"/c","Tags":["服务端","录播平台"],"Attrs":null},{"Name":"远端异地","IP":"***********","Port":80,"Path":"/c","Tags":["服务端","远端异地"],"Attrs":null}]`
