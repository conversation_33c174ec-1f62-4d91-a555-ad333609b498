package controller

import (
	"net/http"

	"github.com/docker/docker/api/types"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
)

// BuildPrune - Delete builder cache
func BuildPrune(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{})
}

// ImageBuild - Build an image
func ImageBuild(c *gin.Context) {
	c.J<PERSON>(http.StatusOK, gin.H{})
}

// ImageCommit - Create a new image from a container
func ImageCommit(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageCreate - Create an image
func ImageCreate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageDelete - Remove an image
// @Summary     删除镜像
// @Schemes     http
// @Description 删除镜像
// @Tags        image
// @Produce     json
// @Router      /v1/images/{name} [delete]
func ImageDelete(c *gin.Context) {
	containerName := c.Params.ByName("name")
	global.GvaLog.Info(containerName)
	res, err := getClient().ImageRemove(ctx, containerName, types.ImageRemoveOptions{})
	if err != nil {
		global.GvaLog.Errorf("Image Remove %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

// ImageGet - Export an image
func ImageGet(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageGetAll - Export several images
func ImageGetAll(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageHistory - Get the history of an image
func ImageHistory(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageInspect - Inspect an image
// @Summary     镜像信息
// @Schemes     http
// @Description 镜像信息
// @Tags        image
// @Produce     json
// @Success     200 {object} model.Image "Inspect a image"
// @Router      /v1/images/{name}/json [get]
func ImageInspect(c *gin.Context) {
	name := c.Param("name")
	res, _, err := getClient().ImageInspectWithRaw(ctx, name)
	if err != nil {
		global.GvaLog.Errorf("Image Inspect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

type ImageM struct {
	ID      string   `json:"id"`
	Status  string   `json:"status"`
	Tag     []string `json:"tag"`
	Size    int64    `json:"size"`
	Created string   `json:"created"`
}

// ImageList - List Images
// @Summary     获取所有镜像
// @Schemes     http
// @Description 获取所有镜像
// @Tags        image
// @Produce     json
// @Success     200 {object} []ImageM "Images list"
// @Router      /v1/images/json [get]
func ImageList(c *gin.Context) {
	images, err := getClient().ImageList(ctx, types.ImageListOptions{All: true})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, images)
}

// ImageLoad - Import images
// @Summary     导入镜像
// @Schemes     http
// @Description 导入镜像
// @Tags        image
// @Produce     json
// @Router      /v1/images/load [post]
func ImageLoad(c *gin.Context) {
	f, err := c.FormFile("file")
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	global.GvaLog.Infof("%s size: %d", f.Filename, f.Size)
	// if err := c.SaveUploadedFile(f, "image/"+f.Filename); err != nil {
	// 	global.GVA_LOG.Error(err)
	// 	c.JSON(http.StatusBadRequest, err)
	// 	return
	// }
	// file, err := os.Open("image/" + f.Filename)
	// if err != nil {
	// 	global.GVA_LOG.Error(err)
	// 	c.JSON(http.StatusBadRequest, err)
	// 	return
	// }
	// global.GVA_LOG.Info(file.Name())
	// defer file.Close()
	file, err := f.Open()
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	res, err := getClient().ImageLoad(ctx, file, false)
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	// err = os.Remove("image/" + f.Filename)
	// if err != nil {
	// 	global.GVA_LOG.Error(err)
	// 	c.JSON(http.StatusBadRequest, err)
	// 	return
	// }
	defer res.Body.Close()
	c.JSON(http.StatusOK, res)
}

// ImagePrune - Delete unused images
func ImagePrune(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImagePush - Push an image
func ImagePush(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageSearch - Search images
func ImageSearch(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ImageTag - Tag an image
func ImageTag(c *gin.Context) {
	imageName := c.Param("name")
	repo, hasRepo := c.GetQuery("repo")
	tag, hasTag := c.GetQuery("tag")
	if hasRepo && hasTag {
		newImageName := repo + ":" + tag
		err := getClient().ImageTag(ctx, imageName, newImageName)
		if err != nil {
			global.GvaLog.Errorf("Image Tag %s", err.Error())
			c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
			return
		}
		c.Status(http.StatusCreated)
	} else {
		c.Status(http.StatusBadRequest)
	}
}
