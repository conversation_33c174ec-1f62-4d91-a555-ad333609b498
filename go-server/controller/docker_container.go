package controller

import (
	"archive/tar"
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/duke-git/lancet/v2/validator"
	"github.com/gin-gonic/gin"

	"mediacomm.com/skylink/global"
	model "mediacomm.com/skylink/model/docker"
)

var timeout int = 5

// ContainerArchive - Get an archive of a filesystem resource in a container
func ContainerArchive(c *gin.Context) {
	containerID := c.Param("id")
	path := c.Query("path")
	rc, cps, err := getClient().CopyFromContainer(ctx, containerID, path)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}

	defer rc.Close()
	fileName := "archive.tar"
	out, err := os.Create(fileName)
	pwd, _ := os.Getwd()
	filePath := filepath.Join(pwd, fileName)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	defer out.Close()
	_, err = io.Copy(out, rc)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	global.GvaLog.Infof("ContainerPathStat:%+v", cps)
	c.File(filePath)
}

func PutContainerArchive(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

func ContainerServiceLog(c *gin.Context) {
	containerID := c.Param("id")
	var res types.ContainerJSON
	res, err := getClient().ContainerInspect(ctx, containerID)
	if err != nil {
		global.GvaLog.Errorf("Container Inspect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	var logPath string
	for _, env := range res.Config.Env {
		envArray := strings.Split(env, "=")
		if len(envArray) != 2 {
			continue
		}
		if envArray[0] == global.LogPath {
			logPath = envArray[1]
		}
	}
	if len(logPath) == 0 {
		c.JSON(http.StatusNotAcceptable, gin.H{"message": "Log path is not set"})
		return
	}
	global.GvaLog.Info("logPath:" + logPath)
	rc, cps, err := getClient().CopyFromContainer(ctx, containerID, logPath)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	defer rc.Close()
	filePath := fmt.Sprintf("/tmp/archive-%s.tar", time.Now())
	out, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	defer out.Close()
	_, err = io.Copy(out, rc)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	global.GvaLog.Infof("ContainerPathStat:%+v", cps)
	c.File(filePath)
}

// ContainerArchiveInfo - Get information about files in a container
func ContainerArchiveInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerAttach - Attach to a container
func ContainerAttach(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerAttachWebsocket - Attach to a container via a websocket
func ContainerAttachWebsocket(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerChanges - Get changes on a container’s filesystem
func ContainerChanges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerCreate - Create a container
// @Summary     创建容器
// @Schemes     http
// @Description 创建容器
// @Tags        container
// @Produce     json
// @Router      /v1/containers/create [post]
func ContainerCreate(c *gin.Context) {
	containerName := c.Query("name")
	var opt model.ContainerCreateConfig
	if err := c.ShouldBindJSON(&opt); err != nil {
		global.GvaLog.Error("[ContainerCreate] Parsing json " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	res, err := getClient().ContainerCreate(ctx, opt.Config, opt.HostConfig, nil, nil, containerName)
	if err != nil {
		global.GvaLog.Errorf("[ContainerCreate] create %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusCreated, res)
}

// ContainerDelete - Remove a container
// @Summary     删除容器
// @Schemes     http
// @Description 删除容器
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id} [delete]
func ContainerDelete(c *gin.Context) {
	containerID := c.Param("id")
	removeVolumes, _ := strconv.ParseBool(c.Query("v"))
	force, err := strconv.ParseBool(c.Query("force"))
	if err != nil {
		force = false
	}
	err = getClient().ContainerRemove(ctx, containerID,
		types.ContainerRemoveOptions{
			RemoveVolumes: removeVolumes,
			Force:         force})
	if err != nil {
		global.GvaLog.Errorf("Container Remove %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// ContainerExport - Export a container
func ContainerExport(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerInspect - Inspect a container
// @Summary     容器信息
// @Schemes     http
// @Description 容器信息
// @Tags        container
// @Produce     json
// @Success     200 {object} model.ContainerInspectResponse "Inspect a container"
// @Router      /v1/containers/{id}/json [get]
func ContainerInspect(c *gin.Context) {
	containerID := c.Param("id")
	var res types.ContainerJSON
	res, err := getClient().ContainerInspect(ctx, containerID)
	if err != nil {
		global.GvaLog.Errorf("Container Inspect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

// ContainerNetwork
// @Summary     容器网络信息
// @Schemes     http
// @Description 容器网络信息
// @Tags        container
// @Produce     json
// @Success     200 {object} []model.ContainerNetwork "Container Network"
// @Router      /v1/containers/{id}/network [get]
func ContainerNetwork(c *gin.Context) {
	containerID := c.Param("id")
	json, err := getClient().ContainerInspect(ctx, containerID)
	if err != nil {
		global.GvaLog.Errorf("Container Inepect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	var res []model.ContainerNetwork
	for k, v := range json.NetworkSettings.Networks {
		res = append(res,
			model.ContainerNetwork{
				ContainerID: containerID,
				NetworkID:   k,
				IPAddress:   v.IPAddress})
	}
	c.JSON(http.StatusOK, res)
}

// ContainerKill - Kill a container
// @Summary     杀死容器进程
// @Schemes     http
// @Description 杀死容器进程
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/kill [post]
func ContainerKill(c *gin.Context) {
	id := c.Param("id")
	err := getClient().ContainerKill(ctx, id, "SIGKILL")
	if err != nil {
		global.GvaLog.Errorf("Container Kill %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// ContainerList - List containers
// @Summary 获取所有容器列表(包括未启动)
// @Schemes http
// @Description
// @Tags    container
// @Produce json
// @Success 200 {object} []model.ContainerM "Containers list"
// @Router  /v1/containers/json [get]
func ContainerList(c *gin.Context) {
	containers, err := getClient().ContainerList(ctx, types.ContainerListOptions{All: true})
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	res := []types.Container{}
	for _, c := range containers {
		if strings.HasPrefix(c.Image, "service-platform") {
			continue
		}
		res = append(res, c)
	}
	resMap := groupContainerByLabel(res, global.ProjectLabel)
	c.JSON(http.StatusOK, resMap)
}

func containerAddress(netSetting map[string]*network.EndpointSettings) string {
	str := []string{}
	for _, v := range netSetting {
		ip := v.IPAddress
		if len(ip) > 0 {
			str = append(str, strings.TrimSpace(ip))
		}
	}
	return strings.Join(str, ",")
}

func groupContainerByLabel(containers []types.Container, labelName string) map[string][]types.Container {
	containersByLabel := map[string][]types.Container{}
	containersByLabel[""] = []types.Container{}
	for _, c := range containers {
		label, ok := c.Labels[labelName]
		if !ok {
			notCompose := containersByLabel[""]
			notCompose = append(notCompose, c)
			containersByLabel[""] = notCompose
			continue
		}
		labelContainers, ok := containersByLabel[label]
		if !ok {
			labelContainers = []types.Container{}
		}
		labelContainers = append(labelContainers, c)
		containersByLabel[label] = labelContainers
	}
	return containersByLabel
}

// ContainerLogs - Get container logs
func ContainerLogs(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerPause - Pause a container
func ContainerPause(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerPrune - Delete stopped containers
func ContainerPrune(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerRename - Rename a container
func ContainerRename(c *gin.Context) {
	containerID := c.Param("id")
	newContainerName := c.Query("name")
	err := getClient().ContainerRename(ctx, containerID, newContainerName)
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// ContainerResize - Resize a container TTY
func ContainerResize(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerRestart - Restart a container
// @Summary     重启容器
// @Schemes     http
// @Description 重启容器
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/restart [post]
func ContainerRestart(c *gin.Context) {
	containerID := c.Param("id")
	err := getClient().ContainerRestart(ctx, containerID, container.StopOptions{Timeout: &timeout})
	if err != nil {
		global.GvaLog.Errorf("Container Restart %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusOK)
}

// ContainerStart - Start a container
// @Summary     启动容器
// @Schemes     http
// @Description 启动容器
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/start [post]
func ContainerStart(c *gin.Context) {
	containerID := c.Param("id")
	err := getClient().ContainerStart(ctx, containerID, types.ContainerStartOptions{})
	if err != nil {
		global.GvaLog.Errorf("Container Start %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// ContainerStats - Get container stats based on resource usage
// @Summary     容器状态
// @Schemes     http
// @Description 容器状态
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/stats [get]
func ContainerStats(c *gin.Context) {
	containerID := c.Param("id")
	stats, err := getClient().ContainerStats(ctx, containerID, false)
	if err != nil {
		global.GvaLog.Errorf("Container Stats %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	body, err := io.ReadAll(stats.Body)
	defer stats.Body.Close()
	if err != nil {
		global.GvaLog.Errorf("Container Stats %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	//res, _ := util.DisableEscapeHtml(string(body))
	c.JSON(http.StatusOK, json.RawMessage(string(body)))
}

// ContainerStop - Stop a container
// @Summary     停止容器
// @Schemes     http
// @Description 停止容器
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/stop [post]
func ContainerStop(c *gin.Context) {
	containerID := c.Param("id")
	err := getClient().ContainerStop(ctx, containerID, container.StopOptions{Timeout: &timeout})
	if err != nil {
		global.GvaLog.Errorf("Container Stop %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// ContainerTop - List processes running inside a container
func ContainerTop(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerUnpause - Unpause a container
func ContainerUnpause(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ContainerUpdate - Update a container
func ContainerUpdate(c *gin.Context) {
	containerID := c.Param("id")
	var update container.UpdateConfig
	if err := c.ShouldBindJSON(&update); err != nil {
		global.GvaLog.Error("[ContainerUpdate] Parsing json " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	res, err := getClient().ContainerUpdate(ctx, containerID, update)
	if err != nil {
		global.GvaLog.Errorf("Container Update %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"warnings": res.Warnings})
}

// ContainerWait - Wait for a container
func ContainerWait(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

var routeMutex sync.Mutex

// ContainerSetDefaultRoute - Set default route
func ContainerSetDefaultRoute(c *gin.Context) {
	routeMutex.Lock()
	defer routeMutex.Unlock()
	containerID := c.Param("id")
	path := "/etc/"
	ip := c.Query("ip")
	if !validator.IsIp(ip) {
		global.GvaLog.Error("The IP parameter is invalid")
		c.JSON(http.StatusBadRequest, gin.H{"message": "The IP parameter is invalid"})
		return
	}
	var srcFile = "default-route"
	var desFile = "default-route.tar"
	err := createFileWithContent(srcFile, ip)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] createFileWithContent fail, %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	err = createTarWithSrc(desFile, srcFile)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] createTarWithSrc fail, %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	f, err := os.Open(desFile)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] Open desFile fail, %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	err = getClient().CopyToContainer(ctx, containerID, path, f, types.CopyToContainerOptions{})
	if err != nil {
		f.Close()
		global.GvaLog.Errorf("[PutContainerArchive] CopyToContainer fail, %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusAccepted)
}

func createFileWithContent(fileName string, content string) error {
	f, err := os.Create(fileName)
	if err != nil {
		return err
	} else {
		_, err = f.Write([]byte(content))
		defer f.Close()
		return err
	}
}
func createTarWithSrc(desFile string, srcFile string) error {
	fw, err := os.Create(desFile)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] create tar fail, %s", err.Error())
		return err
	}
	defer fw.Close()
	tw := tar.NewWriter(fw)
	defer tw.Close()
	fi, err := os.Stat(srcFile)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] get %s stat fail, %s", srcFile, err.Error())
		return err
	}
	hdr, err := tar.FileInfoHeader(fi, "")
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] create header fail, %s", err.Error())
		return err
	}
	err = tw.WriteHeader(hdr)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] write header fail, %s", err.Error())
		return err
	}
	fr, err := os.Open(srcFile)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] %s", err.Error())
		return err
	}
	defer fr.Close()
	_, err = io.Copy(tw, fr)
	if err != nil {
		global.GvaLog.Errorf("[PutContainerArchive] copy info fail, %s", err.Error())
		return err
	}
	return nil
}

// ContainerGetDefaultRoute - set default route
func ContainerGetDefaultRoute(c *gin.Context) {
	containerID := c.Param("id")
	path := "/etc/default-route"
	rc, _, err := getClient().CopyFromContainer(ctx, containerID, path)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Errorf("[ContainerGetDefaultRoute] get route fail, %s", err.Error())
		return
	}
	defer rc.Close()
	tarReader := tar.NewReader(rc)
	header, err := tarReader.Next()
	if err == io.EOF {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error("default route not set")
		return
	} else if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error("default route not set", err.Error())
		return
	}
	info := header.FileInfo()
	if info.IsDir() {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error(path, "is Dir.", "default route not set")
		return
	}
	line := bufio.NewReader(tarReader)
	content, _, err := line.ReadLine()
	if err == io.EOF {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error("default route not set")
		return
	} else if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error("default route not set", err.Error())
		return
	}
	if !validator.IsIp(string(content)) {
		c.JSON(http.StatusNotFound, gin.H{"message": "default route not set"})
		global.GvaLog.Error("default route not set")
		return
	}
	c.JSON(http.StatusOK, gin.H{"defaultRoute": string(content)})

}

// EntrypointsList - List Entrypoints
// @Summary 获取所有服务入口
// @Schemes http
// @Description
// @Tags    container
// @Produce json
// @Success 200 {object} []model.Entrypoints "Entrypoints list"
// @Router  /v1/containers/entrypoints [get]
func ContainerEntrypoints(c *gin.Context) {
	containers, err := getClient().ContainerList(ctx, types.ContainerListOptions{})
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	entres := []model.Entrypoints{}
	for _, container := range containers {
		detail, err := getClient().ContainerInspect(ctx, container.ID)
		if err != nil {
			global.GvaLog.Errorf("Container %s Inspect %s", container.Names[0], err.Error())
			c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
			return
		}
		allIp := []string{}
		for k, es := range detail.NetworkSettings.Networks {
			if k == "bridge" {
				continue
			}
			allIp = append(allIp, es.IPAddress)
		}
		allIndex := mapset.NewSet[int]()
		allNames := map[int]string{}
		allPorts := map[int]int{}
		allPaths := map[int]string{}
		allTags := map[int][]string{}
		allAttrs := map[int]map[string]string{}
		for _, env := range detail.Config.Env {
			if strings.HasPrefix(env, "Entrypoint_") {
				// 去除所有空格
				env = strings.Replace(env, " ", "", -1)
				envMap := strings.Split(env, "=")
				if len(envMap) != 2 {
					continue
				}
				envKey := strings.Split(envMap[0], "_")
				if len(envKey) != 3 {
					continue
				}
				index, err := strconv.Atoi(envKey[1])
				if err != nil {
					global.GvaLog.Error(err.Error())
					continue
				}
				allIndex.Add(index)
				if envKey[2] == "NAME" {
					allNames[index] = envMap[1]
				} else if envKey[2] == "PORT" {
					port, err := strconv.Atoi(envMap[1])
					if err != nil {
						global.GvaLog.Error(err.Error())
						continue
					}
					allPorts[index] = port
				} else if envKey[2] == "PATH" {
					allPaths[index] = envMap[1]
				} else if envKey[2] == "TAGS" {
					allTags[index] = strings.Split(envMap[1], ",")
				} else {
					allAttrs[index] = map[string]string{envKey[2]: envMap[1]}
				}

			}
		}
		for _, ip := range allIp {
			for _, i := range allIndex.ToSlice() {
				entres = append(entres,
					model.Entrypoints{
						Name:  allNames[i],
						IP:    ip,
						Port:  allPorts[i],
						Path:  allPaths[i],
						Tags:  allTags[i],
						Attrs: allAttrs[i]})
			}
		}
	}
	c.JSON(http.StatusOK, entres)
}

// ContainerBackup - Backup a container
// @Summary     备份容器为镜像
// @Schemes     http
// @Description 提交映像时,正在提交的容器及其进程将暂停.
// @Tags        container
// @Produce     json
// @Router      /v1/containers/{id}/backup [post]
func ContainerBackup(c *gin.Context) {
	containerID := c.Param("id")
	info, err := getClient().ContainerInspect(ctx, containerID)
	if err != nil {
		global.GvaLog.Errorf("Container Inspect %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	containerName := info.Name[1:]
	newImageName := fmt.Sprintf("%s:backup-%s", strings.ReplaceAll(info.Config.Image, ":", "-"), time.Now().Format("2006-01-02_15-04-05"))
	comment := fmt.Sprintf("A new image from a container(%s)", containerName)
	cco := types.ContainerCommitOptions{Reference: newImageName, Comment: comment, Pause: true}
	res, err := getClient().ContainerCommit(ctx, containerID, cco)
	if err != nil {
		global.GvaLog.Errorf("Container Commit %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	imageInspect, _, _ := getClient().ImageInspectWithRaw(ctx, res.ID)
	c.JSON(http.StatusOK, imageInspect)
}
