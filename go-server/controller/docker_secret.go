package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SecretCreate - Create a secret
func SecretCreate(c *gin.Context) {
	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{})
}

// SecretDelete - Delete a secret
func SecretDelete(c *gin.Context) {
	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{})
}

// SecretInspect - Inspect a secret
func SecretInspect(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// SecretList - List secrets
func SecretList(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{})
}

// SecretUpdate - Update a Secret
func SecretUpdate(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{})
}
