package controller

import (
	"testing"

	"github.com/gin-gonic/gin"
)

func TestPlatformReboot(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			PlatformReboot(tt.args.c)
		})
	}
}

func TestPlatformAbout(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(_ *testing.T) {
			PlatformAbout(tt.args.c)
		})
	}
}
