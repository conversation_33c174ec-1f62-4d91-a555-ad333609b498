package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ConfigCreate - Create a config
func ConfigCreate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ConfigDelete - Delete a config
func ConfigDelete(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ConfigInspect - Inspect a config
func ConfigInspect(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ConfigList - List configs
func ConfigList(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// ConfigUpdate - Update a Config
func ConfigUpdate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}
