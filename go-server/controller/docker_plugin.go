package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetPluginPrivileges - Get plugin privileges
func GetPluginPrivileges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// PluginCreate - Create a plugin
func PluginCreate(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{})
}

// PluginDelete - Remove a plugin
func PluginDelete(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// PluginDisable - Disable a plugin
func PluginDisable(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// PluginEnable - Enable a plugin
func PluginEnable(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{})
}

// PluginInspect - Inspect a plugin
func PluginInspect(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// PluginList - List plugins
func PluginList(c *gin.Context) {
	c.J<PERSON>(http.StatusOK, gin.H{})
}

// PluginPull - Install a plugin
func PluginPull(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// PluginPush - Push a plugin
func PluginPush(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// PluginSet - Configure a plugin
func PluginSet(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// PluginUpgrade - Upgrade a plugin
func PluginUpgrade(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}
