package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ContainerExec - Create an exec instance
func ContainerExec(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ExecInspect - Inspect an exec instance
func ExecInspect(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ExecResize - Resize an exec instance
func ExecResize(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ExecStart - Start an exec instance
func ExecStart(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}
