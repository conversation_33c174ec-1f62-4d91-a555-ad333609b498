package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// NodeDelete - Delete a node
func NodeDelete(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// NodeInspect - Inspect a node
func NodeInspect(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// NodeList - List nodes
func NodeList(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// NodeUpdate - Update a node
func NodeUpdate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}
