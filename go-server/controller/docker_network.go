package controller

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/network"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
	model "mediacomm.com/skylink/model/docker"
	"mediacomm.com/skylink/utils"
)

// NetworkCreate - Create a network
// @Summary 创建网络
// @Schemes http
// @Description
// @Tags    network
// @Produce json
// @Param   request body model.NetworkCreateRequest true "Network Create Request"
// @Router  /v1/networks/create [post]
func NetworkCreate(c *gin.Context) {
	var opt model.NetworkCreateRequest
	if err := c.ShouldBindJSON(&opt); err != nil {
		global.GvaLog.Error("[NetworkCreate] Parsing json " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.<PERSON>rror()})
		return
	}
	var create types.NetworkCreate
	create.Driver = opt.Driver
	create.CheckDuplicate = opt.CheckDuplicate
	var config []network.IPAMConfig
	for _, cf := range opt.Config {
		config = append(config, network.IPAMConfig{Subnet: cf.Subnet, Gateway: cf.Gateway})
	}
	create.IPAM = &network.IPAM{Config: config}
	create.Options = map[string]string{"parent": opt.Parent}

	// c.JSON(http.StatusOK, create)
	res, err := getClient().NetworkCreate(ctx, opt.Name, create)
	if err != nil {
		global.GvaLog.Errorf("[NetworkCreate] create  %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

// NetworkDelete - Remove a network
// @Summary 删除网络
// @Schemes http
// @Description
// @Tags    network
// @Produce json
// @Param   id path string true "network id"
// @Router  /v1/networks/{id} [delete]
func NetworkDelete(c *gin.Context) {
	networkID := c.Param("id")
	if err := getClient().NetworkRemove(ctx, networkID); err != nil {
		global.GvaLog.Errorf("[NetworkDelete] %s", err.Error())
		errLower := strings.ToLower(err.Error())
		if strings.Contains(errLower, "operation not supported for pre-defined networks") {
			c.JSON(http.StatusForbidden, gin.H{"message": err.Error()})
			return
		} else if strings.Contains(errLower, "no such network") {
			c.JSON(http.StatusNotFound, gin.H{"message": err.Error()})
			return
		} else if strings.Contains(errLower, "server error") {
			c.JSON(http.StatusInternalServerError, gin.H{"message": err.Error()})
			return
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
			return
		}
	}
	c.Status(http.StatusNoContent)
}

// NetworkConnect - Connect a container to a network
// @Summary 将容器连接到网络中
// @Schemes http
// @Description
// @Tags    network
// @Produce json
// @Param   id      path string                      true "network id"
// @Param   request body model.NetworkConnectRequest true "Network Connect Request"
// @Router  /v1/networks/{id}/connect [post]
func NetworkConnect(c *gin.Context) {
	networkID := c.Param("id")
	var opt model.NetworkConnectRequest
	if err := c.ShouldBindJSON(&opt); err != nil {
		global.GvaLog.Error("[NetworkConnect] Parsing json " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	err := getClient().NetworkConnect(ctx, networkID, opt.ContainerID,
		&network.EndpointSettings{IPAMConfig: &network.EndpointIPAMConfig{IPv4Address: opt.IPAddress}})
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// NetworkDisconnect - Disconnect a container from a network
// @Summary 断开一个容器与网络的连接
// @Schemes http
// @Description
// @Tags    network
// @Produce json
// @Param   id      path string                         true "network id"
// @Param   request body model.NetworkDisconnectRequest true "Network Disconnect Request"
// @Router  /v1/networks/{id}/disconnect [post]
func NetworkDisconnect(c *gin.Context) {
	networkID := c.Param("id")
	var opt model.NetworkDisconnectRequest
	if err := c.ShouldBindJSON(&opt); err != nil {
		global.GvaLog.Error("[NetworkDisconnect] Parsing json " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	err := getClient().NetworkDisconnect(ctx, networkID, opt.ContainerID, opt.Force)
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// NetworkInspect - Inspect a network
func NetworkInspect(c *gin.Context) {
	networkID := c.Param("id")
	nr, err := getClient().NetworkInspect(ctx, networkID, types.NetworkInspectOptions{})
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, nr)
}

// NetworkList - List networks
// @Summary 获取网络列表
// @Schemes http
// @Description
// @Tags    network
// @Produce json
// @Success 200 {object} []model.Network "networks list"
// @Router  /v1/networks [get]
func NetworkList(c *gin.Context) {
	networkList, err := getClient().NetworkList(ctx, types.NetworkListOptions{})
	if err != nil {
		global.GvaLog.Errorf("[NetworkList] %s", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	var res []model.Network
	for _, v := range networkList {
		if v.Name == "none" || v.Name == "host" {
			continue
		}
		var netRes model.Network
		netRes.Name = v.Name
		netRes.Id = v.ID
		netRes.Driver = v.Driver
		netRes.Config = []model.IPAMConfig{}
		for _, config := range v.IPAM.Config {
			netRes.Config = append(netRes.Config, model.IPAMConfig{Subnet: config.Subnet, Gateway: config.Gateway})
		}
		netRes.Parent = v.Options["parent"]
		res = append(res, netRes)
	}
	c.JSON(http.StatusOK, res)
}

// NetworkPrune - Delete unused networks
func NetworkPrune(c *gin.Context) {
	res, err := getClient().NetworksPrune(ctx, filters.Args{})
	if err != nil {
		global.GvaLog.Errorf("[NetworkPrune] %s", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, res)
}

// NetworkInterfaces - List Interfaces
// @Summary 获取网卡配置
// @Schemes http
// @Tags    network
// @Produce json
// @Success 200 {object} []interface{} "Interfaces list"
// @Router  /v1/networks/interfaces [get]
func NetworkInterfaces(c *gin.Context) {
	// 使用新的通用获取配置功能
	networkInterfaces, err := utils.GetNetworkInterfaceConfigs()
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	fmt.Println(utils.BeautifyStruct(networkInterfaces))
	c.JSON(http.StatusOK, networkInterfaces)
}
