package controller

import (
	"fmt"
	"testing"
	"time"

	linuxproc "github.com/c9s/goprocinfo/linux"
	"github.com/shirou/gopsutil/cpu"
)

func TestProc(t *testing.T) {
	c, _ := linuxproc.ReadCPUInfo("/proc/cpuinfo")
	fmt.Printf("c.NumCPU(): %+v\n", c.NumCPU())
	fmt.Printf("c.NumCore(): %+v\n", c.NumCore())
	fmt.Printf("c.NumPhysicalCPU(): %+v\n", c.NumPhysicalCPU())

	stat, _ := linuxproc.ReadStat("/proc/stat")
	fmt.Printf("stat.CPUStatAll: %+v\n", stat.CPUStatAll)
	percent, _ := cpu.Percent(time.Second, false)
	fmt.Printf("percent: %+v\n", percent)
	for _, s := range stat.CPUStats {
		fmt.Printf("s: %+v\n", s)
	}
}
