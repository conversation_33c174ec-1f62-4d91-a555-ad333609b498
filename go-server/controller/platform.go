package controller

import (
	"net/http"
	"os"
	"os/exec"
	"path"
	"syscall"

	"github.com/duke-git/lancet/v2/fileutil"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/utils"
)

// PlatformRestart - Restart service platform
// @Summary     重启服务平台
// @Schemes     http
// @Description 重启服务平台,仅在容器化时才可用
// @Tags        platform
// @Produce     json
// @Router      /v1/platform/reboot [post]
func PlatformReboot(_ *gin.Context) {
	if !utils.IsRelease() {
		global.GvaLog.Error("Service is not release mode, can not use platform reboot.")
		return
	}
	syscall.Sync()
	cmd := exec.Command("nsenter", "-t", "1", "-m", "-u", "-i", "-n", "-p", "--", "reboot")
	err := cmd.Run()
	if err != nil {
		global.GvaLog.Error(err)
	}
}

// PlatformAbout - About
// @Summary     关于
// @Schemes     http
// @Description 关于
// @Tags        platform
// @Produce     json
// @Router      /about [get]
func PlatformAbout(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"version": global.AppVersion})
}

// UploadLogo - UploadLogo
// @Summary     替换logo
// @Schemes     http
// @Description 替换logo
// @Tags        platform
// @Produce     json
// @Router      /v1/platform/uploadLogo [post]
func UploadLogo(c *gin.Context) {
	if !fileutil.IsExist(global.ImagePath) || !fileutil.IsDir(global.ImagePath) {
		os.Mkdir(global.ImagePath, os.ModePerm)
	}
	f, err := c.FormFile("file")
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	var finalName string
	imgFor := c.Query("type")
	if utils.CheckImageType(f.Filename, global.ImagePngType) {
		switch imgFor {
		case global.CompanyLogo:
			finalName = global.CompanyLogo + "." + global.ImagePngType
		case global.CompanyLogoMini:
			finalName = global.CompanyLogoMini + "." + global.ImagePngType
		case global.LoginLogo:
			finalName = global.LoginLogo + "." + global.ImagePngType
		case global.LoginBackground:
			finalName = global.LoginBackground + "." + global.ImagePngType
		default:
			global.GvaLog.Error("参数type非法")
			c.JSON(http.StatusBadRequest, gin.H{"message": "参数type非法"})
			return
		}
	} else if utils.CheckImageType(f.Filename, global.ImageIcoType) {
		switch imgFor {
		case global.FAVICON:
			finalName = global.FAVICON + "." + global.ImageIcoType
		default:
			global.GvaLog.Error("参数type非法")
			c.JSON(http.StatusBadRequest, gin.H{"message": "参数type非法"})
			return
		}
	} else {
		global.GvaLog.Error("文件类型非法")
		c.JSON(http.StatusBadRequest, gin.H{"message": "文件类型非法"})
		return
	}
	filepath := path.Join(global.ImagePath, finalName)
	err = c.SaveUploadedFile(f, filepath)
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusNotAcceptable, err.Error())
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "upload successfully!"})
}
