package controller

import (
	"fmt"
	"net/http"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/model/system"
)

func GetUserInfo(c *gin.Context) {
	claims := jwt.ExtractClaims(c)
	fmt.Printf("claims: %v\n", claims)
	userName := claims["username"].(string)

	c.JSON(http.StatusOK, gin.H{
		"message": userName,
	})
}

type Auth struct {
	Username string `json:"username" binding:"required,gte=5,lte=30"` // 用户名
	Password string `json:"password"`                                 // 密码
	Role     string `json:"role"`                                     // 角色
	Comment  string `json:"comment"`                                  // 用户角色
}

type UsernameStruct struct {
	Username string `json:"username" binding:"required,gte=5,lte=30"` // 用户名
}

func UsersInfo(c *gin.Context) {
	usersInfo, err := system.GetAllUsersInfo()
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.J<PERSON>(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, usersInfo)
}

func UserInfo(c *gin.Context) {
	username := c.Param("username")
	userInfo, err := system.GetUserInfo(username)
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, userInfo)
}

func UserRegister(c *gin.Context) {
	var vals Auth
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	if err := system.UserRegister(vals.Username, vals.Password, vals.Role, vals.Comment); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
}

func UserChangePassword(c *gin.Context) {
	var vals Auth
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	if err := system.ChangePassword(vals.Username, vals.Password); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
}

func UserChangeRole(c *gin.Context) {
	var vals Auth
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	if err := system.ChangeRole(vals.Username, vals.Role); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
}

func UserChangeComment(c *gin.Context) {
	var vals Auth
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	if err := system.ChangeComment(vals.Username, vals.Comment); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
}

func UserDelete(c *gin.Context) {
	var vals UsernameStruct
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	if err := system.DeleteUser(vals.Username); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
}
