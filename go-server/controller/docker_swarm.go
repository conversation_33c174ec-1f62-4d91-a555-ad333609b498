package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SwarmInit - Initialize a new swarm
func SwarmInit(c *gin.Context) {
	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{})
}

// SwarmInspect - Inspect swarm
func SwarmInspect(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// SwarmJoin - Join an existing swarm
func SwarmJoin(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// SwarmLeave - Leave a swarm
func SwarmLeave(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{})
}

// SwarmUnlock - Unlock a locked manager
func SwarmUnlock(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{})
}

// SwarmUnlockkey - Get the unlock key
func SwarmUnlockkey(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{})
}

// SwarmUpdate - Update a swarm
func SwarmUpdate(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}
