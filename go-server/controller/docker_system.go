package controller

import (
	"net/http"

	"github.com/docker/docker/api/types"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
)

// SystemAuth - Check auth configuration
func SystemAuth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// SystemDataUsage - Get data usage information
func SystemDataUsage(c *gin.Context) {
	du, err := getClient().DiskUsage(ctx, types.DiskUsageOptions{})
	if err != nil {
		global.GvaLog.Errorf("[SystemDataUsage] GetDiskUsage fail, %s", err.Error())
		c.<PERSON>(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	c.J<PERSON>(http.StatusOK, du)
}

// SystemEvents - Monitor events
func SystemEvents(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// SystemInfo - Get system information
func SystemInfo(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{})
}

// SystemPing - Ping
func SystemPing(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// SystemPingHead - Ping
func SystemPingHead(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// SystemVersion - Get version
func SystemVersion(c *gin.Context) {
	version := getClient().ClientVersion()
	c.JSON(http.StatusOK, gin.H{"DOCKER_API_VERSION": version})
}
