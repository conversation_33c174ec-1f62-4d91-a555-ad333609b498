package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ServiceCreate - Create a service
func ServiceCreate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ServiceDelete - Delete a service
func ServiceDelete(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{})
}

// ServiceInspect - Inspect a service
func ServiceInspect(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ServiceList - List services
func ServiceList(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ServiceLogs - Get service logs
func ServiceLogs(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}

// ServiceUpdate - Update a service
func ServiceUpdate(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{})
}
