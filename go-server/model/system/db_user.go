package system

import (
	"errors"
	"time"

	"mediacomm.com/skylink/global"
)

// 声明芯片类型
type RoleType string

const (
	EditorRole   string = "editor"   // 普通用户
	ReadOnlyRole string = "readonly" // 只读用户
)

type Auth struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	Username  string `gorm:"comment:用户登录名"`  // 用户登录名
	Password  string `gorm:"comment:用户登录密码"` // 用户登录密码
	Role      string `gorm:"comment:用户角色"`   // 用户角色
	Comment   string `gorm:"comment:说明"`     // 用户角色
}

func (Auth) TableName() string {
	return "db_auth"
}

// User demo
type User struct {
	UserName string `json:"username"`
	Role     string `json:"role"`
}

func validateRole(role string) string {
	if role == EditorRole || role == ReadOnlyRole {
		return role
	}
	return ReadOnlyRole
}

func CheckAuth(username, password string) bool {
	var auth Auth
	// password = utils.MD5V([]byte(password))
	row := global.GvaDb.Where("username = ? AND password = ?", username, password).Find(&auth).RowsAffected
	return row == 1
}

func GetAllUsersInfo() ([]Auth, error) {
	var users []Auth
	result := global.GvaDb.Select("id", "created_at", "updated_at", "username", "role", "comment").Find(&users)
	if result.Error != nil {
		return nil, result.Error
	}
	return users, nil
}

func GetUserInfo(username string) (*Auth, error) {
	var user Auth
	result := global.GvaDb.Select("id", "created_at", "updated_at", "username", "role", "comment").Where("username = ?", username).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

func UserRegister(username, password, role, comment string) error {
	isExist := CheckUserExist(username, &Auth{})
	if isExist {
		return errors.New("用户已存在,注册失败")
	} else {
		var auth Auth
		auth.Username = username
		auth.Password = password
		auth.Role = validateRole(role)
		auth.Comment = comment
		result := global.GvaDb.Create(&auth)
		if result.RowsAffected == 1 {
			return nil
		} else {
			return errors.New("用户不存在,但注册失败")
		}
	}
}

func ChangePassword(username, password string) error {
	var auth Auth
	isExist := CheckUserExist(username, &auth)
	if isExist {
		err := global.GvaDb.Model(&Auth{}).Where("username = ?", username).Update("password", password).Error
		if err != nil {
			return err
		}
		return nil
	} else {
		return errors.New("用户不存在,改密码失败")
	}
}

func DeleteUser(username string) error {
	if username == "admin" {
		return errors.New("不允许删除管理员账号")
	}
	var auth Auth
	isExist := CheckUserExist(username, &auth)
	if isExist {
		err := global.GvaDb.Delete(&auth).Error
		if err != nil {
			return err
		}
		return nil
	} else {
		return errors.New("用户不存在,删除失败")
	}
}

func CheckUserExist(username string, auth *Auth) bool {
	row := global.GvaDb.Where("username = ?", username).Find(auth).RowsAffected
	return row > 0
}

func ChangeRole(username, role string) error {
	isExist := CheckUserExist(username, &Auth{})
	if isExist {
		role = validateRole(role)
		err := global.GvaDb.Model(&Auth{}).Where("username = ?", username).Update("role", role).Error
		if err != nil {
			return err
		}
		return nil
	} else {
		return errors.New("用户不存在,修改用户角色失败")
	}
}

func ChangeComment(username, comment string) error {
	isExist := CheckUserExist(username, &Auth{})
	if isExist {
		err := global.GvaDb.Model(&Auth{}).Where("username = ?", username).Update("comment", comment).Error
		if err != nil {
			return err
		}
		return nil
	} else {
		return errors.New("用户不存在,修改用户说明失败")
	}
}

func GetUserRole(username string) string {
	var auth Auth
	err := global.GvaDb.Where("username = ?", username).First(&auth).Error
	if err != nil {
		return ReadOnlyRole
	}
	return auth.Role
}
