package system

import (
	"net"

	"github.com/duke-git/lancet/v2/slice"
)

type NetworkInterfaces []NetworkInterface

// NetworkInterfaces
type NetworkInterface struct {
	Name    string `json:"name"` // 网卡名
	DHCP    bool   `json:"dhcp"`
	Address string `json:"address"` // IP地址
	Netmask string `json:"netmask"` // IP掩码
	Gateway string `json:"gateway"` // IP网关
}

func (conf NetworkInterfaces) Validate() bool {
	countPredicate := func(index int, item NetworkInterface) bool {
		return net.ParseIP(item.Gateway) != nil
	}
	findPredicate := func(index int, item NetworkInterface) bool {
		if len(item.Name) == 0 {
			return true
		}
		if item.DHCP {
			if net.ParseIP(item.Address) != nil || net.ParseIP(item.Netmask) != nil || net.ParseIP(item.Gateway) != nil {
				return true
			}
		} else {
			if net.ParseIP(item.Address) == nil || net.ParseIP(item.Netmask) == nil {
				return true
			}
		}
		return false
	}
	if _, ok := slice.FindBy(conf, findPredicate); ok {
		return false
	}
	if slice.CountBy(conf, countPredicate) > 1 {
		return false
	}
	return true
}
