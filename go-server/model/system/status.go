package system

type SysInfo struct {
	Cpu     cpu       `json:"cpu"`
	Disk    disk      `json:"disk"`
	Ram     ram       `json:"ram"`
	Network []Network `json:"network"`
	Gateway   string   `json:"gateway,omitempty"`
}

type cpu struct {
	Cores string  `json:"cores"`
	Used  float64 `json:"used"`
}

type disk struct {
	Used  uint64 `json:"used"`
	Total uint64 `json:"total"`
}

type ram struct {
	Used  uint64 `json:"used"`
	Total uint64 `json:"total"`
}

type Network struct {
	Name      string   `json:"name"`
	Connected bool     `json:"connected"`
	Ip        []string `json:"ip"`
}
