package model

// TaskSpecContainerSpecFile1 - File represents a specific target that is backed by a file.  <p><br /><p>  > **Note**: `Configs.File` and `Configs.Runtime` are mutually exclusive 
type TaskSpecContainerSpecFile1 struct {

	// Name represents the final filename in the filesystem. 
	Name string `json:"Name,omitempty"`

	// UID represents the file UID.
	UID string `json:"UID,omitempty"`

	// GID represents the file GID.
	GID string `json:"GID,omitempty"`

	// Mode represents the FileMode of the file.
	Mode int32 `json:"Mode,omitempty"`
}
