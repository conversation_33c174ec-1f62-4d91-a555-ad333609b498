package model

type Service struct {

	ID string `json:"ID,omitempty"`

	Version ObjectVersion `json:"Version,omitempty"`

	CreatedAt string `json:"CreatedAt,omitempty"`

	UpdatedAt string `json:"UpdatedAt,omitempty"`

	Spec ServiceSpec `json:"Spec,omitempty"`

	Endpoint ServiceEndpoint `json:"Endpoint,omitempty"`

	UpdateStatus ServiceUpdateStatus `json:"UpdateStatus,omitempty"`

	ServiceStatus ServiceServiceStatus `json:"ServiceStatus,omitempty"`

	JobStatus ServiceJobStatus `json:"JobStatus,omitempty"`
}
