package model

// SwarmInfo - Represents generic information about swarm. 
type SwarmInfo struct {

	// Unique identifier of for this node in the swarm.
	NodeID string `json:"NodeID,omitempty"`

	// IP address at which this node can be reached by other nodes in the swarm. 
	NodeAddr string `json:"NodeAddr,omitempty"`

	LocalNodeState LocalNodeState `json:"LocalNodeState,omitempty"`

	ControlAvailable bool `json:"ControlAvailable,omitempty"`

	Error string `json:"Error,omitempty"`

	// List of ID's and addresses of other managers in the swarm. 
	RemoteManagers *[]PeerNode `json:"RemoteManagers,omitempty"`

	// Total number of nodes in the swarm.
	Nodes *int32 `json:"Nodes,omitempty"`

	// Total number of managers in the swarm.
	Managers *int32 `json:"Managers,omitempty"`

	Cluster *ClusterInfo `json:"Cluster,omitempty"`
}
