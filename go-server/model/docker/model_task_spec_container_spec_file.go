package model

// TaskSpecContainerSpecFile - File represents a specific target that is backed by a file. 
type TaskSpecContainerSpecFile struct {

	// Name represents the final filename in the filesystem. 
	Name string `json:"Name,omitempty"`

	// UID represents the file UID.
	UID string `json:"UID,omitempty"`

	// GID represents the file GID.
	GID string `json:"GID,omitempty"`

	// Mode represents the FileMode of the file.
	Mode int32 `json:"Mode,omitempty"`
}
