package model

type Image struct {

	Id string `json:"Id"`

	RepoTags []string `json:"RepoTags,omitempty"`

	RepoDigests []string `json:"RepoDigests,omitempty"`

	Parent string `json:"Parent"`

	Comment string `json:"Comment"`

	Created string `json:"Created"`

	Container string `json:"Container"`

	ContainerConfig ContainerConfig `json:"ContainerConfig,omitempty"`

	DockerVersion string `json:"DockerVersion"`

	Author string `json:"Author"`

	Config ContainerConfig `json:"Config,omitempty"`

	Architecture string `json:"Architecture"`

	Os string `json:"Os"`

	OsVersion string `json:"OsVersion,omitempty"`

	Size int64 `json:"Size"`

	VirtualSize int64 `json:"VirtualSize"`

	GraphDriver GraphDriverData `json:"GraphDriver"`

	RootFS ImageRootFs `json:"RootFS"`

	Metadata ImageMetadata `json:"Metadata,omitempty"`
}
