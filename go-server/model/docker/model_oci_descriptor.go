package model

// OciDescriptor - A descriptor struct containing digest, media type, and size, as defined in the [OCI Content Descriptors Specification](https://github.com/opencontainers/image-spec/blob/v1.0.1/descriptor.md). 
type OciDescriptor struct {

	// The media type of the object this schema refers to. 
	MediaType string `json:"mediaType,omitempty"`

	// The digest of the targeted content. 
	Digest string `json:"digest,omitempty"`

	// The size in bytes of the blob. 
	Size int64 `json:"size,omitempty"`
}
