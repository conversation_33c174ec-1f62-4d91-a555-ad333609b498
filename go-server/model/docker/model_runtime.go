package model

// Runtime - Runtime describes an [OCI compliant](https://github.com/opencontainers/runtime-spec) runtime.  The runtime is invoked by the daemon via the `containerd` daemon. OCI runtimes act as an interface to the Linux kernel namespaces, cgroups, and SELinux. 
type Runtime struct {

	// Name and, optional, path, of the OCI executable binary.  If the path is omitted, the daemon searches the host's `$PATH` for the binary and uses the first result. 
	Path string `json:"path,omitempty"`

	// List of command-line arguments to pass to the runtime when invoked. 
	RuntimeArgs *[]string `json:"runtimeArgs,omitempty"`
}
