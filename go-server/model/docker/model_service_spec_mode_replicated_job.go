package model

// ServiceSpecModeReplicatedJob - The mode used for services with a finite number of tasks that run to a completed state. 
type ServiceSpecModeReplicatedJob struct {

	// The maximum number of replicas to run simultaneously. 
	MaxConcurrent int64 `json:"MaxConcurrent,omitempty"`

	// The total number of replicas desired to reach the Completed state. If unset, will default to the value of `MaxConcurrent` 
	TotalCompletions int64 `json:"TotalCompletions,omitempty"`
}
