package model

type SwarmInitRequest struct {

	// Listen address used for inter-manager communication, as well as determining the networking interface used for the VXLAN Tunnel Endpoint (VTEP). This can either be an address/port combination in the form `***********:4567`, or an interface followed by a port number, like `eth0:4567`. If the port number is omitted, the default swarm listening port is used. 
	ListenAddr string `json:"ListenAddr,omitempty"`

	// Externally reachable address advertised to other nodes. This can either be an address/port combination in the form `***********:4567`, or an interface followed by a port number, like `eth0:4567`. If the port number is omitted, the port number from the listen address is used. If `AdvertiseAddr` is not specified, it will be automatically detected when possible. 
	AdvertiseAddr string `json:"AdvertiseAddr,omitempty"`

	// Address or interface to use for data path traffic (format: `<ip|interface>`), for example,  `***********`, or an interface, like `eth0`. If `DataPathAddr` is unspecified, the same address as `AdvertiseAddr` is used.  The `DataPathAddr` specifies the address that global scope network drivers will publish towards other  nodes in order to reach the containers running on this node. Using this parameter it is possible to separate the container data traffic from the management traffic of the cluster. 
	DataPathAddr string `json:"DataPathAddr,omitempty"`

	// DataPathPort specifies the data path port number for data traffic. Acceptable port range is 1024 to 49151. if no port is set or is set to 0, default port 4789 will be used. 
	DataPathPort int32 `json:"DataPathPort,omitempty"`

	// Default Address Pool specifies default subnet pools for global scope networks. 
	DefaultAddrPool []string `json:"DefaultAddrPool,omitempty"`

	// Force creation of a new swarm.
	ForceNewCluster bool `json:"ForceNewCluster,omitempty"`

	// SubnetSize specifies the subnet size of the networks created from the default subnet pool. 
	SubnetSize int32 `json:"SubnetSize,omitempty"`

	Spec SwarmSpec `json:"Spec,omitempty"`
}
