package model

type TaskSpecContainerSpecConfigs struct {

	File TaskSpecContainerSpecFile1 `json:"File,omitempty"`

	// Runtime represents a target that is not mounted into the container but is used by the task  <p><br /><p>  > **Note**: `Configs.File` and `Configs.Runtime` are mutually > exclusive 
	Runtime map[string]interface{} `json:"Runtime,omitempty"`

	// ConfigID represents the ID of the specific config that we're referencing. 
	ConfigID string `json:"ConfigID,omitempty"`

	// ConfigName is the name of the config that this references, but this is just provided for lookup/display purposes. The config in the reference will be identified by its ID. 
	ConfigName string `json:"ConfigName,omitempty"`
}
