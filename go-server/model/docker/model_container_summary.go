package model

type ContainerSummary struct {

	// The ID of this container
	Id string `json:"Id,omitempty"`

	// The names that this container has been given
	Names []string `json:"Names,omitempty"`

	// The name of the image used when creating this container
	Image string `json:"Image,omitempty"`

	// The ID of the image that this container was created from
	ImageID string `json:"ImageID,omitempty"`

	// Command to run when starting the container
	Command string `json:"Command,omitempty"`

	// When the container was created
	Created int64 `json:"Created,omitempty"`

	// The ports exposed by this container
	Ports []Port `json:"Ports,omitempty"`

	// The size of files that have been created or changed by this container
	SizeRw int64 `json:"SizeRw,omitempty"`

	// The total size of all the files in this container
	SizeRootFs int64 `json:"SizeRootFs,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`

	// The state of this container (e.g. `Exited`)
	State string `json:"State,omitempty"`

	// Additional human-readable status of this container (e.g. `Exit 0`)
	Status string `json:"Status,omitempty"`

	HostConfig ContainerSummaryHostConfig `json:"HostConfig,omitempty"`

	NetworkSettings ContainerSummaryNetworkSettings `json:"NetworkSettings,omitempty"`

	Mounts []Mount `json:"Mounts,omitempty"`
}
