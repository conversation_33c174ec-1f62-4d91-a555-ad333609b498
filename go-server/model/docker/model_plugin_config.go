package model

// PluginConfig - The config of a plugin.
type PluginConfig struct {

	// Docker Version used to create the plugin
	DockerVersion string `json:"DockerVersion,omitempty"`

	Description string `json:"Description"`

	Documentation string `json:"Documentation"`

	Interface PluginConfigInterface `json:"Interface"`

	Entrypoint []string `json:"Entrypoint"`

	WorkDir string `json:"WorkDir"`

	User PluginConfigUser `json:"User,omitempty"`

	Network PluginConfigNetwork `json:"Network"`

	Linux PluginConfigLinux `json:"Linux"`

	PropagatedMount string `json:"PropagatedMount"`

	IpcHost bool `json:"IpcHost"`

	PidHost bool `json:"PidHost"`

	Mounts []PluginMount `json:"Mounts"`

	Env []PluginEnv `json:"Env"`

	Args PluginConfigArgs `json:"Args"`

	Rootfs PluginConfigRootfs `json:"rootfs,omitempty"`
}
