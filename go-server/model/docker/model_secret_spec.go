package model

type SecretSpec struct {

	// User-defined name of the secret.
	Name string `json:"Name,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`

	// Base64-url-safe-encoded ([RFC 4648](https://tools.ietf.org/html/rfc4648#section-5)) data to store as secret.  This field is only used to _create_ a secret, and is not returned by other endpoints. 
	Data string `json:"Data,omitempty"`

	Driver Driver `json:"Driver,omitempty"`

	Templating Driver `json:"Templating,omitempty"`
}
