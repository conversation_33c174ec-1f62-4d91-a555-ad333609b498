package model

// TaskSpecContainerSpecDnsConfig - Specification for DNS related configurations in resolver configuration file (`resolv.conf`). 
type TaskSpecContainerSpecDnsConfig struct {

	// The IP addresses of the name servers.
	Nameservers []string `json:"Nameservers,omitempty"`

	// A search list for host-name lookup.
	Search []string `json:"Search,omitempty"`

	// A list of internal resolver variables to be modified (e.g., `debug`, `ndots:3`, etc.). 
	Options []string `json:"Options,omitempty"`
}
