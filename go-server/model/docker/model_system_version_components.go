package model

type SystemVersionComponents struct {

	// Name of the component 
	Name string `json:"Name"`

	// Version of the component 
	Version string `json:"Version"`

	// Key/value pairs of strings with additional information about the component. These values are intended for informational purposes only, and their content is not defined, and not part of the API specification.  These messages can be printed by the client as information to the user. 
	Details *map[string]interface{} `json:"Details,omitempty"`
}
