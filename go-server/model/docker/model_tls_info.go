package model

// TlsInfo - Information about the issuer of leaf TLS certificates and the trusted root CA certificate. 
type TlsInfo struct {

	// The root CA certificate(s) that are used to validate leaf TLS certificates. 
	TrustRoot string `json:"TrustRoot,omitempty"`

	// The base64-url-safe-encoded raw subject bytes of the issuer.
	CertIssuerSubject string `json:"CertIssuerSubject,omitempty"`

	// The base64-url-safe-encoded raw public key bytes of the issuer. 
	CertIssuerPublicKey string `json:"CertIssuerPublicKey,omitempty"`
}
