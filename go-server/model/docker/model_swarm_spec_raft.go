package model

// SwarmSpecRaft - Raft configuration.
type SwarmSpecRaft struct {

	// The number of log entries between snapshots.
	SnapshotInterval int32 `json:"SnapshotInterval,omitempty"`

	// The number of snapshots to keep beyond the current snapshot. 
	KeepOldSnapshots int32 `json:"KeepOldSnapshots,omitempty"`

	// The number of log entries to keep around to sync up slow followers after a snapshot is created. 
	LogEntriesForSlowFollowers int32 `json:"LogEntriesForSlowFollowers,omitempty"`

	// The number of ticks that a follower will wait for a message from the leader before becoming a candidate and starting an election. `ElectionTick` must be greater than `HeartbeatTick`.  A tick currently defaults to one second, so these translate directly to seconds currently, but this is NOT guaranteed. 
	ElectionTick int32 `json:"ElectionTick,omitempty"`

	// The number of ticks between heartbeats. Every HeartbeatTick ticks, the leader will send a heartbeat to the followers.  A tick currently defaults to one second, so these translate directly to seconds currently, but this is NOT guaranteed. 
	HeartbeatTick int32 `json:"HeartbeatTick,omitempty"`
}
