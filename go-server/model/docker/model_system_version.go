package model

// SystemVersion - Response of Engine API: GET \"/version\" 
type SystemVersion struct {

	Platform SystemVersionPlatform `json:"Platform,omitempty"`

	// Information about system components 
	Components []SystemVersionComponents `json:"Components,omitempty"`

	// The version of the daemon
	Version string `json:"Version,omitempty"`

	// The default (and highest) API version that is supported by the daemon 
	ApiVersion string `json:"ApiVersion,omitempty"`

	// The minimum API version that is supported by the daemon 
	MinAPIVersion string `json:"MinAPIVersion,omitempty"`

	// The Git commit of the source code that was used to build the daemon 
	GitCommit string `json:"GitCommit,omitempty"`

	// The version Go used to compile the daemon, and the version of the Go runtime in use. 
	GoVersion string `json:"GoVersion,omitempty"`

	// The operating system that the daemon is running on (\"linux\" or \"windows\") 
	Os string `json:"Os,omitempty"`

	// The architecture that the daemon is running on 
	Arch string `json:"Arch,omitempty"`

	// The kernel version (`uname -r`) that the daemon is running on.  This field is omitted when empty. 
	KernelVersion string `json:"KernelVersion,omitempty"`

	// Indicates if the daemon is started with experimental features enabled.  This field is omitted when empty / false. 
	Experimental bool `json:"Experimental,omitempty"`

	// The date and time that the daemon was compiled. 
	BuildTime string `json:"BuildTime,omitempty"`
}
