package model

type TaskSpecContainerSpecSecrets struct {

	File TaskSpecContainerSpecFile `json:"File,omitempty"`

	// SecretID represents the ID of the specific secret that we're referencing. 
	SecretID string `json:"SecretID,omitempty"`

	// SecretName is the name of the secret that this references, but this is just provided for lookup/display purposes. The secret in the reference will be identified by its ID. 
	SecretName string `json:"SecretName,omitempty"`
}
