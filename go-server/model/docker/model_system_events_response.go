package model

// SystemEventsResponse - EventMessage represents the information an event contains. 
type SystemEventsResponse struct {

	// The type of object emitting the event
	Type string `json:"Type,omitempty"`

	// The type of event
	Action string `json:"Action,omitempty"`

	Actor EventActor `json:"Actor,omitempty"`

	// Scope of the event. Engine events are `local` scope. Cluster (Swarm) events are `swarm` scope. 
	Scope string `json:"scope,omitempty"`

	// Timestamp of event
	Time int64 `json:"time,omitempty"`

	// Timestamp of event, with nanosecond accuracy
	TimeNano int64 `json:"timeNano,omitempty"`
}
