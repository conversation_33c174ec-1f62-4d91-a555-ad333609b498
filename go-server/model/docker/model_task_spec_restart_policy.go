package model

// TaskSpecRestartPolicy - Specification for the restart policy which applies to containers created as part of this service. 
type TaskSpecRestartPolicy struct {

	// Condition for restart.
	Condition string `json:"Condition,omitempty"`

	// Delay between restart attempts.
	Delay int64 `json:"Delay,omitempty"`

	// Maximum attempts to restart a given container before giving up (default value is 0, which is ignored). 
	MaxAttempts int64 `json:"MaxAttempts,omitempty"`

	// Windows is the time window used to evaluate the restart policy (default value is 0, which is unbounded). 
	Window int64 `json:"Window,omitempty"`
}
