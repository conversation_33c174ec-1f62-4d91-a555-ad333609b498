package model

// TaskSpecNetworkAttachmentSpec - Read-only spec type for non-swarm containers attached to swarm overlay networks.  <p><br /></p>  > **Note**: ContainerSpec, NetworkAttachmentSpec, and PluginSpec are > mutually exclusive. PluginSpec is only used when the Runtime field > is set to `plugin`. NetworkAttachmentSpec is used when the Runtime > field is set to `attachment`. 
type TaskSpecNetworkAttachmentSpec struct {

	// ID of the container represented by this task
	ContainerID string `json:"ContainerID,omitempty"`
}
