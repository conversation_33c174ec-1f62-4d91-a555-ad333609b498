package model

// TaskSpecPluginSpec - Plugin spec for the service.  *(Experimental release only.)*  <p><br /></p>  > **Note**: ContainerSpec, NetworkAttachmentSpec, and PluginSpec are > mutually exclusive. PluginSpec is only used when the Runtime field > is set to `plugin`. NetworkAttachmentSpec is used when the Runtime > field is set to `attachment`. 
type TaskSpecPluginSpec struct {

	// The name or 'alias' to use for the plugin.
	Name string `json:"Name,omitempty"`

	// The plugin image reference to use.
	Remote string `json:"Remote,omitempty"`

	// Disable the plugin once scheduled.
	Disabled bool `json:"Disabled,omitempty"`

	PluginPrivilege []PluginPrivilege `json:"PluginPrivilege,omitempty"`
}
