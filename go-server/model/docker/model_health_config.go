package model

// HealthConfig - A test to perform to check that the container is healthy.
type HealthConfig struct {

	// The test to perform. Possible values are:  - `[]` inherit healthcheck from image or parent image - `[\"NONE\"]` disable healthcheck - `[\"CMD\", args...]` exec arguments directly - `[\"CMD-SHELL\", command]` run command with system's default shell 
	Test []string `json:"Test,omitempty"`

	// The time to wait between checks in nanoseconds. It should be 0 or at least 1000000 (1 ms). 0 means inherit. 
	Interval int32 `json:"Interval,omitempty"`

	// The time to wait before considering the check to have hung. It should be 0 or at least 1000000 (1 ms). 0 means inherit. 
	Timeout int32 `json:"Timeout,omitempty"`

	// The number of consecutive failures needed to consider a container as unhealthy. 0 means inherit. 
	Retries int32 `json:"Retries,omitempty"`

	// Start period for the container to initialize before starting health-retries countdown in nanoseconds. It should be 0 or at least 1000000 (1 ms). 0 means inherit. 
	StartPeriod int32 `json:"StartPeriod,omitempty"`
}
