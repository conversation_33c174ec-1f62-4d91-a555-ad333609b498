package model

// VolumeConfig - Volume configuration
type VolumeConfig struct {

	// The new volume's name. If not specified, <PERSON><PERSON> generates a name. 
	Name string `json:"Name,omitempty"`

	// Name of the volume driver to use.
	Driver string `json:"Driver,omitempty"`

	// A mapping of driver options and values. These options are passed directly to the driver and are driver specific. 
	DriverOpts map[string]string `json:"DriverOpts,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`
}
