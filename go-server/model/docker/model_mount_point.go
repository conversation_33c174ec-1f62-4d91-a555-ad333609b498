package model

// MountPoint - A mount point inside a container
type MountPoint struct {

	Type string `json:"Type,omitempty"`

	Name string `json:"Name,omitempty"`

	Source string `json:"Source,omitempty"`

	Destination string `json:"Destination,omitempty"`

	Driver string `json:"Driver,omitempty"`

	Mode string `json:"Mode,omitempty"`

	RW bool `json:"RW,omitempty"`

	Propagation string `json:"Propagation,omitempty"`
}
