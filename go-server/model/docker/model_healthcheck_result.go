package model

import (
	"time"
)

// HealthcheckResult - HealthcheckResult stores information about a single run of a healthcheck probe 
type HealthcheckResult struct {

	// Date and time at which this check started in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	Start time.Time `json:"Start,omitempty"`

	// Date and time at which this check ended in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	End string `json:"End,omitempty"`

	// ExitCode meanings:  - `0` healthy - `1` unhealthy - `2` reserved (considered unhealthy) - other values: error running probe 
	ExitCode int32 `json:"ExitCode,omitempty"`

	// Output from last check
	Output string `json:"Output,omitempty"`
}
