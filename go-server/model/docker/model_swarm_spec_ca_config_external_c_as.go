package model

type SwarmSpecCaConfigExternalCAs struct {

	// Protocol for communication with the external CA (currently only `cfssl` is supported). 
	Protocol string `json:"Protocol,omitempty"`

	// URL where certificate signing requests should be sent. 
	URL string `json:"URL,omitempty"`

	// An object with key/value pairs that are interpreted as protocol-specific options for the external CA driver. 
	Options map[string]string `json:"Options,omitempty"`

	// The root CA certificate (in PEM format) this external CA uses to issue TLS certificates (assumed to be to the current swarm root CA certificate if not provided). 
	CACert string `json:"CACert,omitempty"`
}
