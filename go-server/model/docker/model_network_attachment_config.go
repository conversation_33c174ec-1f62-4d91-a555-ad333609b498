package model

// NetworkAttachmentConfig - Specifies how a service should be attached to a particular network. 
type NetworkAttachmentConfig struct {

	// The target network for attachment. Must be a network name or ID. 
	Target string `json:"Target,omitempty"`

	// Discoverable alternate names for the service on this network. 
	Aliases []string `json:"Aliases,omitempty"`

	// Driver attachment options for the network target. 
	DriverOpts map[string]string `json:"DriverOpts,omitempty"`
}
