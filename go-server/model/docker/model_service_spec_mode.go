package model

// ServiceSpecMode - Scheduling mode for the service.
type ServiceSpecMode struct {

	Replicated ServiceSpecModeReplicated `json:"Replicated,omitempty"`

	Global map[string]interface{} `json:"Global,omitempty"`

	ReplicatedJob ServiceSpecModeReplicatedJob `json:"ReplicatedJob,omitempty"`

	// The mode used for services which run a task to the completed state on each valid node. 
	GlobalJob map[string]interface{} `json:"GlobalJob,omitempty"`
}
