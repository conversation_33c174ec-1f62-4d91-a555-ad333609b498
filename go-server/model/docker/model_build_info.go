package model

type BuildInfo struct {

	Id string `json:"id,omitempty"`

	Stream string `json:"stream,omitempty"`

	Error string `json:"error,omitempty"`

	ErrorDetail ErrorDetail `json:"errorDetail,omitempty"`

	Status string `json:"status,omitempty"`

	Progress string `json:"progress,omitempty"`

	ProgressDetail ProgressDetail `json:"progressDetail,omitempty"`

	Aux ImageId `json:"aux,omitempty"`
}
