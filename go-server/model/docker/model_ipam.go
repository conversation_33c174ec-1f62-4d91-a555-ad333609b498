package model

type Ipam struct {

	// Name of the IPAM driver to use.
	Driver string `json:"Driver,omitempty"`

	// List of IPAM configuration options, specified as a map:  ``` {\"Subnet\": <CIDR>, \"IPRange\": <CIDR>, \"Gateway\": <IP address>, \"AuxAddress\": <device_name:IP address>} ``` 
	Config []map[string]string `json:"Config,omitempty"`

	// Driver-specific options, specified as a map.
	Options map[string]string `json:"Options,omitempty"`
}
