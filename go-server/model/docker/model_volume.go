package model

type Volume struct {

	// Name of the volume.
	Name string `json:"Name"`

	// Name of the volume driver used by the volume.
	Driver string `json:"Driver"`

	// Mount path of the volume on the host.
	Mountpoint string `json:"Mountpoint"`

	// Date/Time the volume was created.
	CreatedAt string `json:"CreatedAt,omitempty"`

	// Low-level details about the volume, provided by the volume driver. Details are returned as a map with key/value pairs: `{\"key\":\"value\",\"key2\":\"value2\"}`.  The `Status` field is optional, and is omitted if the volume driver does not support this feature. 
	Status map[string]map[string]interface{} `json:"Status,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels"`

	// The level at which the volume exists. Either `global` for cluster-wide, or `local` for machine level. 
	Scope string `json:"Scope"`

	// The driver specific options used when creating the volume. 
	Options map[string]string `json:"Options"`

	UsageData *VolumeUsageData `json:"UsageData,omitempty"`
}
