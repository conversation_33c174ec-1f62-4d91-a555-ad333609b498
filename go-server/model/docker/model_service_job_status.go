package model

// ServiceJobStatus - The status of the service when it is in one of ReplicatedJob or GlobalJob modes. Absent on Replicated and Global mode services. The JobIteration is an ObjectVersion, but unlike the Service's version, does not need to be sent with an update request. 
type ServiceJobStatus struct {

	JobIteration ObjectVersion `json:"JobIteration,omitempty"`

	// The last time, as observed by the server, that this job was started. 
	LastExecution string `json:"LastExecution,omitempty"`
}
