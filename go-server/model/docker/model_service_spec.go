package model

// ServiceSpec - User modifiable configuration for a service.
type ServiceSpec struct {

	// Name of the service.
	Name string `json:"Name,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`

	TaskTemplate TaskSpec `json:"TaskTemplate,omitempty"`

	Mode ServiceSpecMode `json:"Mode,omitempty"`

	UpdateConfig ServiceSpecUpdateConfig `json:"UpdateConfig,omitempty"`

	RollbackConfig ServiceSpecRollbackConfig `json:"RollbackConfig,omitempty"`

	// Specifies which networks the service should attach to.
	Networks []NetworkAttachmentConfig `json:"Networks,omitempty"`

	EndpointSpec EndpointSpec `json:"EndpointSpec,omitempty"`
}
