package model

type EndpointPortConfig struct {

	Name string `json:"Name,omitempty"`

	Protocol string `json:"Protocol,omitempty"`

	// The port inside the container.
	TargetPort int32 `json:"TargetPort,omitempty"`

	// The port on the swarm hosts.
	PublishedPort int32 `json:"PublishedPort,omitempty"`

	// The mode in which port is published.  <p><br /></p>  - \"ingress\" makes the target port accessible on every node,   regardless of whether there is a task for the service running on   that node or not. - \"host\" bypasses the routing mesh and publish the port directly on   the swarm node where that service is running. 
	PublishMode string `json:"PublishMode,omitempty"`
}
