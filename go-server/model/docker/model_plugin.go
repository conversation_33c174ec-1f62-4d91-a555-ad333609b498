package model

// Plugin - A plugin for the Engine API
type Plugin struct {

	Id string `json:"Id,omitempty"`

	Name string `json:"Name"`

	// True if the plugin is running. False if the plugin is not running, only installed.
	Enabled bool `json:"Enabled"`

	Settings PluginSettings `json:"Settings"`

	// plugin remote reference used to push/pull the plugin
	PluginReference string `json:"PluginReference,omitempty"`

	Config PluginConfig `json:"Config"`
}
