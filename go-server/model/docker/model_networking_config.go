package model

// NetworkingConfig - NetworkingConfig represents the container's networking configuration for each of its interfaces. It is used for the networking configs specified in the `docker create` and `docker network connect` commands. 
type NetworkingConfig struct {

	// A mapping of network name to endpoint configuration for that network. 
	EndpointsConfig map[string]EndpointSettings `json:"EndpointsConfig,omitempty"`
}
