package model

type NetworkCreateRequest struct {
	Name string `json:"name"`

	Driver string `json:"driver,omitempty"`

	Config []IPAMConfig `json:"config,omitempty"`

	Parent string `json:"parent,omitempty"`

	CheckDuplicate bool `json:"checkDuplicate,omitempty"`
}

// IP Address Management configurations
type IPAMConfig struct {
	Subnet string `json:"subnet,omitempty"`

	Gateway string `json:"gateway,omitempty"`
}
