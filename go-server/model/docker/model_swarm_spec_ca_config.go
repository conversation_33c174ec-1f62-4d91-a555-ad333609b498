package model

// SwarmSpecCaConfig - CA configuration.
type SwarmSpecCaConfig struct {

	// The duration node certificates are issued for.
	NodeCertExpiry int64 `json:"NodeCertExpiry,omitempty"`

	// Configuration for forwarding signing requests to an external certificate authority. 
	ExternalCAs []SwarmSpecCaConfigExternalCAs `json:"ExternalCAs,omitempty"`

	// The desired signing CA certificate for all swarm node TLS leaf certificates, in PEM format. 
	SigningCACert string `json:"SigningCACert,omitempty"`

	// The desired signing CA key for all swarm node TLS leaf certificates, in PEM format. 
	SigningCAKey string `json:"SigningCAKey,omitempty"`

	// An integer whose purpose is to force swarm to generate a new signing CA certificate and key, if none have been specified in `SigningCACert` and `SigningCAKey` 
	ForceRotate int32 `json:"ForceRotate,omitempty"`
}
