package model

// Resources - A container's resources (cgroups config, ulimits, etc)
type Resources struct {

	// An integer value representing this container's relative CPU weight versus other containers. 
	CpuShares int32 `json:"CpuShares,omitempty"`

	// Memory limit in bytes.
	Memory int64 `json:"Memory,omitempty"`

	// Path to `cgroups` under which the container's `cgroup` is created. If the path is not absolute, the path is considered to be relative to the `cgroups` path of the init process. Cgroups are created if they do not already exist. 
	CgroupParent string `json:"CgroupParent,omitempty"`

	// Block IO weight (relative weight).
	BlkioWeight int32 `json:"BlkioWeight,omitempty"`

	// Block IO weight (relative device weight) in the form:  ``` [{\"Path\": \"device_path\", \"Weight\": weight}] ``` 
	BlkioWeightDevice []ResourcesBlkioWeightDevice `json:"BlkioWeightDevice,omitempty"`

	// Limit read rate (bytes per second) from a device, in the form:  ``` [{\"Path\": \"device_path\", \"Rate\": rate}] ``` 
	BlkioDeviceReadBps []ThrottleDevice `json:"BlkioDeviceReadBps,omitempty"`

	// Limit write rate (bytes per second) to a device, in the form:  ``` [{\"Path\": \"device_path\", \"Rate\": rate}] ``` 
	BlkioDeviceWriteBps []ThrottleDevice `json:"BlkioDeviceWriteBps,omitempty"`

	// Limit read rate (IO per second) from a device, in the form:  ``` [{\"Path\": \"device_path\", \"Rate\": rate}] ``` 
	BlkioDeviceReadIOps []ThrottleDevice `json:"BlkioDeviceReadIOps,omitempty"`

	// Limit write rate (IO per second) to a device, in the form:  ``` [{\"Path\": \"device_path\", \"Rate\": rate}] ``` 
	BlkioDeviceWriteIOps []ThrottleDevice `json:"BlkioDeviceWriteIOps,omitempty"`

	// The length of a CPU period in microseconds.
	CpuPeriod int64 `json:"CpuPeriod,omitempty"`

	// Microseconds of CPU time that the container can get in a CPU period. 
	CpuQuota int64 `json:"CpuQuota,omitempty"`

	// The length of a CPU real-time period in microseconds. Set to 0 to allocate no time allocated to real-time tasks. 
	CpuRealtimePeriod int64 `json:"CpuRealtimePeriod,omitempty"`

	// The length of a CPU real-time runtime in microseconds. Set to 0 to allocate no time allocated to real-time tasks. 
	CpuRealtimeRuntime int64 `json:"CpuRealtimeRuntime,omitempty"`

	// CPUs in which to allow execution (e.g., `0-3`, `0,1`). 
	CpusetCpus string `json:"CpusetCpus,omitempty"`

	// Memory nodes (MEMs) in which to allow execution (0-3, 0,1). Only effective on NUMA systems. 
	CpusetMems string `json:"CpusetMems,omitempty"`

	// A list of devices to add to the container.
	Devices []DeviceMapping `json:"Devices,omitempty"`

	// a list of cgroup rules to apply to the container
	DeviceCgroupRules []string `json:"DeviceCgroupRules,omitempty"`

	// A list of requests for devices to be sent to device drivers. 
	DeviceRequests []DeviceRequest `json:"DeviceRequests,omitempty"`

	// Kernel memory limit in bytes.  <p><br /></p>  > **Deprecated**: This field is deprecated as the kernel 5.4 deprecated > `kmem.limit_in_bytes`. 
	KernelMemory int64 `json:"KernelMemory,omitempty"`

	// Hard limit for kernel TCP buffer memory (in bytes).
	KernelMemoryTCP int64 `json:"KernelMemoryTCP,omitempty"`

	// Memory soft limit in bytes.
	MemoryReservation int64 `json:"MemoryReservation,omitempty"`

	// Total memory limit (memory + swap). Set as `-1` to enable unlimited swap. 
	MemorySwap int64 `json:"MemorySwap,omitempty"`

	// Tune a container's memory swappiness behavior. Accepts an integer between 0 and 100. 
	MemorySwappiness int64 `json:"MemorySwappiness,omitempty"`

	// CPU quota in units of 10<sup>-9</sup> CPUs.
	NanoCpus int64 `json:"NanoCpus,omitempty"`

	// Disable OOM Killer for the container.
	OomKillDisable bool `json:"OomKillDisable,omitempty"`

	// Run an init inside the container that forwards signals and reaps processes. This field is omitted if empty, and the default (as configured on the daemon) is used. 
	Init *bool `json:"Init,omitempty"`

	// Tune a container's PIDs limit. Set `0` or `-1` for unlimited, or `null` to not change. 
	PidsLimit *int64 `json:"PidsLimit,omitempty"`

	// A list of resource limits to set in the container. For example:  ``` {\"Name\": \"nofile\", \"Soft\": 1024, \"Hard\": 2048} ``` 
	Ulimits []ResourcesUlimits `json:"Ulimits,omitempty"`

	// The number of usable CPUs (Windows only).  On Windows Server containers, the processor resource controls are mutually exclusive. The order of precedence is `CPUCount` first, then `CPUShares`, and `CPUPercent` last. 
	CpuCount int64 `json:"CpuCount,omitempty"`

	// The usable percentage of the available CPUs (Windows only).  On Windows Server containers, the processor resource controls are mutually exclusive. The order of precedence is `CPUCount` first, then `CPUShares`, and `CPUPercent` last. 
	CpuPercent int64 `json:"CpuPercent,omitempty"`

	// Maximum IOps for the container system drive (Windows only)
	IOMaximumIOps int64 `json:"IOMaximumIOps,omitempty"`

	// Maximum IO in bytes per second for the container system drive (Windows only). 
	IOMaximumBandwidth int64 `json:"IOMaximumBandwidth,omitempty"`
}
