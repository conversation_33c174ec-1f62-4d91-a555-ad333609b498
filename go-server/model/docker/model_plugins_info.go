package model

// PluginsInfo - Available plugins per type.  <p><br /></p>  > **Note**: Only unmanaged (V1) plugins are included in this list. > V1 plugins are \"lazily\" loaded, and are not returned in this list > if there is no resource using the plugin. 
type PluginsInfo struct {

	// Names of available volume-drivers, and network-driver plugins.
	Volume []string `json:"Volume,omitempty"`

	// Names of available network-drivers, and network-driver plugins.
	Network []string `json:"Network,omitempty"`

	// Names of available authorization plugins.
	Authorization []string `json:"Authorization,omitempty"`

	// Names of available logging-drivers, and logging-driver plugins.
	Log []string `json:"Log,omitempty"`
}
