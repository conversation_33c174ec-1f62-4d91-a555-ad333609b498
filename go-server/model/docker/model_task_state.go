package model

type TaskState string

// List of TaskState
const (
	NEW TaskState = "new"
	ALLOCATED TaskState = "allocated"
	PENDING_TASK TaskState = "pending"
	ASSIGNED TaskState = "assigned"
	ACCEPTED TaskState = "accepted"
	PREPARING TaskState = "preparing"
	READY_TASK TaskState = "ready"
	STARTING TaskState = "starting"
	RUNNING TaskState = "running"
	COMPLETE TaskState = "complete"
	SHUTDOWN TaskState = "shutdown"
	FAILED TaskState = "failed"
	REJECTED TaskState = "rejected"
	REMOVE TaskState = "remove"
	ORPHANED TaskState = "orphaned"
)
