package model

// SwarmSpecTaskDefaultsLogDriver - The log driver to use for tasks created in the orchestrator if unspecified by a service.  Updating this value only affects new tasks. Existing tasks continue to use their previously configured log driver until recreated. 
type SwarmSpecTaskDefaultsLogDriver struct {

	// The log driver to use as a default for new tasks. 
	Name string `json:"Name,omitempty"`

	// Driver-specific options for the selectd log driver, specified as key/value pairs. 
	Options map[string]string `json:"Options,omitempty"`
}
