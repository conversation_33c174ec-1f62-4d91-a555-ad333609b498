package model

// ServiceSpecRollbackConfig - Specification for the rollback strategy of the service.
type ServiceSpecRollbackConfig struct {

	// Maximum number of tasks to be rolled back in one iteration (0 means unlimited parallelism). 
	Parallelism int64 `json:"Parallelism,omitempty"`

	// Amount of time between rollback iterations, in nanoseconds. 
	Delay int64 `json:"Delay,omitempty"`

	// Action to take if an rolled back task fails to run, or stops running during the rollback. 
	FailureAction string `json:"FailureAction,omitempty"`

	// Amount of time to monitor each rolled back task for failures, in nanoseconds. 
	Monitor int64 `json:"Monitor,omitempty"`

	// The fraction of tasks that may fail during a rollback before the failure action is invoked, specified as a floating point number between 0 and 1. 
	MaxFailureRatio float32 `json:"MaxFailureRatio,omitempty"`

	// The order of operations when rolling back a task. Either the old task is shut down before the new task is started, or the new task is started before the old task is shut down. 
	Order string `json:"Order,omitempty"`
}
