package model

// ServiceSpecUpdateConfig - Specification for the update strategy of the service.
type ServiceSpecUpdateConfig struct {

	// Maximum number of tasks to be updated in one iteration (0 means unlimited parallelism). 
	Parallelism int64 `json:"Parallelism,omitempty"`

	// Amount of time between updates, in nanoseconds.
	Delay int64 `json:"Delay,omitempty"`

	// Action to take if an updated task fails to run, or stops running during the update. 
	FailureAction string `json:"FailureAction,omitempty"`

	// Amount of time to monitor each updated task for failures, in nanoseconds. 
	Monitor int64 `json:"Monitor,omitempty"`

	// The fraction of tasks that may fail during an update before the failure action is invoked, specified as a floating point number between 0 and 1. 
	MaxFailureRatio float32 `json:"MaxFailureRatio,omitempty"`

	// The order of operations when rolling out an updated task. Either the old task is shut down before the new task is started, or the new task is started before the old task is shut down. 
	Order string `json:"Order,omitempty"`
}
