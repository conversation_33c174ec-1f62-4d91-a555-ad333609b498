package model

// ClusterInfo - ClusterInfo represents information about the swarm as is returned by the \"/info\" endpoint. Join-tokens are not included. 
type ClusterInfo struct {

	// The ID of the swarm.
	ID string `json:"ID,omitempty"`

	Version ObjectVersion `json:"Version,omitempty"`

	// Date and time at which the swarm was initialised in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	CreatedAt string `json:"CreatedAt,omitempty"`

	// Date and time at which the swarm was last updated in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	UpdatedAt string `json:"UpdatedAt,omitempty"`

	Spec SwarmSpec `json:"Spec,omitempty"`

	TLSInfo TlsInfo `json:"TLSInfo,omitempty"`

	// Whether there is currently a root CA rotation in progress for the swarm 
	RootRotationInProgress bool `json:"RootRotationInProgress,omitempty"`

	// DataPathPort specifies the data path port number for data traffic. Acceptable port range is 1024 to 49151. If no port is set or is set to 0, the default port (4789) is used. 
	DataPathPort int32 `json:"DataPathPort,omitempty"`

	// Default Address Pool specifies default subnet pools for global scope networks. 
	DefaultAddrPool []string `json:"DefaultAddrPool,omitempty"`

	// SubnetSize specifies the subnet size of the networks created from the default subnet pool. 
	SubnetSize int32 `json:"SubnetSize,omitempty"`
}
