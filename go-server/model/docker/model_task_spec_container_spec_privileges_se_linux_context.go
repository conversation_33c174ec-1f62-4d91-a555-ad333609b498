package model

// TaskSpecContainerSpecPrivilegesSeLinuxContext - SELinux labels of the container
type TaskSpecContainerSpecPrivilegesSeLinuxContext struct {

	// Disable SELinux
	Disable bool `json:"Disable,omitempty"`

	// SELinux user label
	User string `json:"User,omitempty"`

	// SELinux role label
	Role string `json:"Role,omitempty"`

	// SELinux type label
	Type string `json:"Type,omitempty"`

	// SELinux level label
	Level string `json:"Level,omitempty"`
}
