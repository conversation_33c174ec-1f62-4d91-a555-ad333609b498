package model

type ExecInspectResponse struct {

	CanRemove bool `json:"CanRemove,omitempty"`

	DetachKeys string `json:"DetachKeys,omitempty"`

	ID string `json:"ID,omitempty"`

	Running bool `json:"Running,omitempty"`

	ExitCode int32 `json:"ExitCode,omitempty"`

	ProcessConfig ProcessConfig `json:"ProcessConfig,omitempty"`

	OpenStdin bool `json:"OpenStdin,omitempty"`

	OpenStderr bool `json:"OpenStderr,omitempty"`

	OpenStdout bool `json:"OpenStdout,omitempty"`

	ContainerID string `json:"ContainerID,omitempty"`

	// The system process ID for the exec process.
	Pid int32 `json:"Pid,omitempty"`
}
