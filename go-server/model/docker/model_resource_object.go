package model

// ResourceObject - An object describing the resources which can be advertised by a node and requested by a task. 
type ResourceObject struct {

	NanoCPUs int64 `json:"NanoCPUs,omitempty"`

	MemoryBytes int64 `json:"MemoryBytes,omitempty"`

	// User-defined resources can be either Integer resources (e.g, `SSD=3`) or String resources (e.g, `GPU=UUID1`). 
	GenericResources []map[string]interface{} `json:"GenericResources,omitempty"`
}
