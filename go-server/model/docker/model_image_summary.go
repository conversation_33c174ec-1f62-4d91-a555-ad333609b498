package model

type ImageSummary struct {

	Id string `json:"Id"`

	ParentId string `json:"ParentId"`

	RepoTags []string `json:"RepoTags"`

	RepoDigests []string `json:"RepoDigests"`

	Created int32 `json:"Created"`

	Size int32 `json:"Size"`

	SharedSize int32 `json:"SharedSize"`

	VirtualSize int32 `json:"VirtualSize"`

	Labels map[string]string `json:"Labels"`

	Containers int32 `json:"Containers"`
}
