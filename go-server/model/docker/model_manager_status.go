package model

// ManagerStatus - ManagerStatus represents the status of a manager.  It provides the current status of a node's manager component, if the node is a manager. 
type ManagerStatus struct {

	Leader bool `json:"Leader,omitempty"`

	Reachability Reachability `json:"Reachability,omitempty"`

	// The IP address and port at which the manager is reachable. 
	Addr string `json:"Addr,omitempty"`
}
