package model

// NetworkSettings - NetworkSettings exposes the network settings in the API
type NetworkSettings struct {

	// Name of the network'a bridge (for example, `docker0`).
	Bridge string `json:"Bridge,omitempty"`

	// SandboxID uniquely represents a container's network stack.
	SandboxID string `json:"SandboxID,omitempty"`

	// Indicates if hairpin NAT should be enabled on the virtual interface. 
	HairpinMode bool `json:"HairpinMode,omitempty"`

	// IPv6 unicast address using the link-local prefix.
	LinkLocalIPv6Address string `json:"LinkLocalIPv6Address,omitempty"`

	// Prefix length of the IPv6 unicast address.
	LinkLocalIPv6PrefixLen int32 `json:"LinkLocalIPv6PrefixLen,omitempty"`

	// PortMap describes the mapping of container ports to host ports, using the container's port-number and protocol as key in the format `<port>/<protocol>`, for example, `80/udp`.  If a container's port is mapped for multiple protocols, separate entries are added to the mapping table. 
	Ports map[string][]PortBinding `json:"Ports,omitempty"`

	// <PERSON><PERSON><PERSON><PERSON> identifies the sandbox
	SandboxKey string `json:"SandboxKey,omitempty"`

	SecondaryIPAddresses *[]Address `json:"SecondaryIPAddresses,omitempty"`

	SecondaryIPv6Addresses *[]Address `json:"SecondaryIPv6Addresses,omitempty"`

	// EndpointID uniquely represents a service endpoint in a Sandbox.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	EndpointID string `json:"EndpointID,omitempty"`

	// Gateway address for the default \"bridge\" network.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	Gateway string `json:"Gateway,omitempty"`

	// Global IPv6 address for the default \"bridge\" network.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	GlobalIPv6Address string `json:"GlobalIPv6Address,omitempty"`

	// Mask length of the global IPv6 address.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	GlobalIPv6PrefixLen int32 `json:"GlobalIPv6PrefixLen,omitempty"`

	// IPv4 address for the default \"bridge\" network.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	IPAddress string `json:"IPAddress,omitempty"`

	// Mask length of the IPv4 address.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	IPPrefixLen int32 `json:"IPPrefixLen,omitempty"`

	// IPv6 gateway address for this network.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	IPv6Gateway string `json:"IPv6Gateway,omitempty"`

	// MAC address for the container on the default \"bridge\" network.  <p><br /></p>  > **Deprecated**: This field is only propagated when attached to the > default \"bridge\" network. Use the information from the \"bridge\" > network inside the `Networks` map instead, which contains the same > information. This field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0 
	MacAddress string `json:"MacAddress,omitempty"`

	// Information about all networks that the container is connected to. 
	Networks map[string]EndpointSettings `json:"Networks,omitempty"`
}
