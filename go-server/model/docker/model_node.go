package model

type Node struct {

	ID string `json:"ID,omitempty"`

	Version ObjectVersion `json:"Version,omitempty"`

	// Date and time at which the node was added to the swarm in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	CreatedAt string `json:"CreatedAt,omitempty"`

	// Date and time at which the node was last updated in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	UpdatedAt string `json:"UpdatedAt,omitempty"`

	Spec NodeSpec `json:"Spec,omitempty"`

	Description NodeDescription `json:"Description,omitempty"`

	Status NodeStatus `json:"Status,omitempty"`

	ManagerStatus *ManagerStatus `json:"ManagerStatus,omitempty"`
}
