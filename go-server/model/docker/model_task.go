package model

type Task struct {

	// The ID of the task.
	ID string `json:"ID,omitempty"`

	Version ObjectVersion `json:"Version,omitempty"`

	CreatedAt string `json:"CreatedAt,omitempty"`

	UpdatedAt string `json:"UpdatedAt,omitempty"`

	// Name of the task.
	Name string `json:"Name,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`

	Spec TaskSpec `json:"Spec,omitempty"`

	// The ID of the service this task is part of.
	ServiceID string `json:"ServiceID,omitempty"`

	Slot int32 `json:"Slot,omitempty"`

	// The ID of the node that this task is on.
	NodeID string `json:"NodeID,omitempty"`

	// User-defined resources can be either Integer resources (e.g, `SSD=3`) or String resources (e.g, `GPU=UUID1`). 
	AssignedGenericResources []map[string]interface{} `json:"AssignedGenericResources,omitempty"`

	Status TaskStatus `json:"Status,omitempty"`

	DesiredState TaskState `json:"DesiredState,omitempty"`

	JobIteration ObjectVersion `json:"JobIteration,omitempty"`
}
