package model

// RestartPolicy - The behavior to apply when the container exits. The default is not to restart.  An ever increasing delay (double the previous delay, starting at 100ms) is added before each restart to prevent flooding the server. 
type RestartPolicy struct {

	// - Empty string means not to restart - `no` Do not automatically restart - `always` Always restart - `unless-stopped` Restart always except when the user has manually stopped the container - `on-failure` Restart only when the container exit code is non-zero 
	Name string `json:"Name,omitempty"`

	// If `on-failure` is used, the number of times to retry before giving up. 
	MaximumRetryCount int32 `json:"MaximumRetryCount,omitempty"`
}
