package model

// DeviceRequest - A request for devices to be sent to device drivers
type DeviceRequest struct {

	Driver string `json:"Driver,omitempty"`

	Count int32 `json:"Count,omitempty"`

	DeviceIDs []string `json:"DeviceIDs,omitempty"`

	// A list of capabilities; an OR list of AND lists of capabilities. 
	Capabilities [][]string `json:"Capabilities,omitempty"`

	// Driver-specific options, specified as a key/value pairs. These options are passed directly to the driver. 
	Options map[string]string `json:"Options,omitempty"`
}
