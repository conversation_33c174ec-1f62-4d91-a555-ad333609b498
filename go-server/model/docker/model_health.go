package model

// Health - Health stores information about the container's healthcheck results. 
type Health struct {

	// Status is one of `none`, `starting`, `healthy` or `unhealthy`  - \"none\"      Indicates there is no healthcheck - \"starting\"  Starting indicates that the container is not yet ready - \"healthy\"   Healthy indicates that the container is running correctly - \"unhealthy\" Unhealthy indicates that the container has a problem 
	Status string `json:"Status,omitempty"`

	// FailingStreak is the number of consecutive failures
	FailingStreak int32 `json:"FailingStreak,omitempty"`

	// Log contains the last few results (oldest first) 
	Log []HealthcheckResult `json:"Log,omitempty"`
}
