package model

// SwarmSpec - User modifiable swarm configuration.
type SwarmSpec struct {

	// Name of the swarm.
	Name string `json:"Name,omitempty"`

	// User-defined key/value metadata.
	Labels map[string]string `json:"Labels,omitempty"`

	Orchestration *SwarmSpecOrchestration `json:"Orchestration,omitempty"`

	Raft SwarmSpecRaft `json:"Raft,omitempty"`

	Dispatcher *SwarmSpecDispatcher `json:"Dispatcher,omitempty"`

	CAConfig *SwarmSpecCaConfig `json:"CAConfig,omitempty"`

	EncryptionConfig SwarmSpecEncryptionConfig `json:"EncryptionConfig,omitempty"`

	TaskDefaults SwarmSpecTaskDefaults `json:"TaskDefaults,omitempty"`
}
