package model

type BuildCache struct {

	ID string `json:"ID,omitempty"`

	Parent string `json:"Parent,omitempty"`

	Type string `json:"Type,omitempty"`

	Description string `json:"Description,omitempty"`

	InUse bool `json:"InUse,omitempty"`

	Shared bool `json:"Shared,omitempty"`

	// Amount of disk space used by the build cache (in bytes). 
	Size int32 `json:"Size,omitempty"`

	// Date and time at which the build cache was created in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	CreatedAt string `json:"CreatedAt,omitempty"`

	// Date and time at which the build cache was last used in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format with nano-seconds. 
	LastUsedAt *string `json:"LastUsedAt,omitempty"`

	UsageCount int32 `json:"UsageCount,omitempty"`
}
