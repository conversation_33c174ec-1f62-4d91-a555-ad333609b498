package model

// EndpointSettings - Configuration for a network endpoint.
type EndpointSettings struct {

	IPAMConfig *EndpointIpamConfig `json:"IPAMConfig,omitempty"`

	Links []string `json:"Links,omitempty"`

	Aliases []string `json:"Aliases,omitempty"`

	// Unique ID of the network. 
	NetworkID string `json:"NetworkID,omitempty"`

	// Unique ID for the service endpoint in a Sandbox. 
	EndpointID string `json:"EndpointID,omitempty"`

	// Gateway address for this network. 
	Gateway string `json:"Gateway,omitempty"`

	// IPv4 address. 
	IPAddress string `json:"IPAddress,omitempty"`

	// Mask length of the IPv4 address. 
	IPPrefixLen int32 `json:"IPPrefixLen,omitempty"`

	// IPv6 gateway address. 
	IPv6Gateway string `json:"IPv6Gateway,omitempty"`

	// Global IPv6 address. 
	GlobalIPv6Address string `json:"GlobalIPv6Address,omitempty"`

	// Mask length of the global IPv6 address. 
	GlobalIPv6PrefixLen int64 `json:"GlobalIPv6PrefixLen,omitempty"`

	// MAC address for the endpoint on this network. 
	MacAddress string `json:"MacAddress,omitempty"`

	// DriverOpts is a mapping of driver options and values. These options are passed directly to the driver and are driver specific. 
	DriverOpts *map[string]string `json:"DriverOpts,omitempty"`
}
