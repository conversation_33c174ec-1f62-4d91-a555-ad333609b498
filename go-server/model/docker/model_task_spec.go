package model

// TaskSpec - User modifiable task configuration.
type TaskSpec struct {

	PluginSpec TaskSpecPluginSpec `json:"PluginSpec,omitempty"`

	ContainerSpec TaskSpecContainerSpec `json:"ContainerSpec,omitempty"`

	NetworkAttachmentSpec TaskSpecNetworkAttachmentSpec `json:"NetworkAttachmentSpec,omitempty"`

	Resources TaskSpecResources `json:"Resources,omitempty"`

	RestartPolicy TaskSpecRestartPolicy `json:"RestartPolicy,omitempty"`

	Placement TaskSpecPlacement `json:"Placement,omitempty"`

	// A counter that triggers an update even if no relevant parameters have been changed. 
	ForceUpdate int32 `json:"ForceUpdate,omitempty"`

	// Runtime is the type of runtime specified for the task executor. 
	Runtime string `json:"Runtime,omitempty"`

	// Specifies which networks the service should attach to.
	Networks []NetworkAttachmentConfig `json:"Networks,omitempty"`

	LogDriver TaskSpecLogDriver `json:"LogDriver,omitempty"`
}
