package model

// EndpointSpec - Properties that can be configured to access and load balance a service.
type EndpointSpec struct {

	// The mode of resolution to use for internal load balancing between tasks. 
	Mode string `json:"Mode,omitempty"`

	// List of exposed ports that this service is accessible on from the outside. Ports can only be provided if `vip` resolution mode is used. 
	Ports []EndpointPortConfig `json:"Ports,omitempty"`
}
