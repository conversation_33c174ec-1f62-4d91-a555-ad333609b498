package model

// OciPlatform - Describes the platform which the image in the manifest runs on, as defined in the [OCI Image Index Specification](https://github.com/opencontainers/image-spec/blob/v1.0.1/image-index.md). 
type OciPlatform struct {

	// The CPU architecture, for example `amd64` or `ppc64`. 
	Architecture string `json:"architecture,omitempty"`

	// The operating system, for example `linux` or `windows`. 
	Os string `json:"os,omitempty"`

	// Optional field specifying the operating system version, for example on Windows `10.0.19041.1165`. 
	OsVersion string `json:"os.version,omitempty"`

	// Optional field specifying an array of strings, each listing a required OS feature (for example on Windows `win32k`). 
	OsFeatures []string `json:"os.features,omitempty"`

	// Optional field specifying a variant of the CPU, for example `v7` to specify ARMv7 when architecture is `arm`. 
	Variant string `json:"variant,omitempty"`
}
