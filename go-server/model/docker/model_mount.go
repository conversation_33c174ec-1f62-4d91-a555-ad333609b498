package model

type Mount struct {

	// Container path.
	Target string `json:"Target,omitempty"`

	// Mount source (e.g. a volume name, a host path).
	Source string `json:"Source,omitempty"`

	// The mount type. Available types:  - `bind` Mounts a file or directory from the host into the container. Must exist prior to creating the container. - `volume` Creates a volume with the given name and options (or uses a pre-existing volume with the same name and options). These are **not** removed when the container is removed. - `tmpfs` Create a tmpfs with the given options. The mount source cannot be specified for tmpfs. - `npipe` Mounts a named pipe from the host into the container. Must exist prior to creating the container. 
	Type string `json:"Type,omitempty"`

	// Whether the mount should be read-only.
	ReadOnly bool `json:"ReadOnly,omitempty"`

	// The consistency requirement for the mount: `default`, `consistent`, `cached`, or `delegated`.
	Consistency string `json:"Consistency,omitempty"`

	BindOptions MountBindOptions `json:"BindOptions,omitempty"`

	VolumeOptions MountVolumeOptions `json:"VolumeOptions,omitempty"`

	TmpfsOptions MountTmpfsOptions `json:"TmpfsOptions,omitempty"`
}
