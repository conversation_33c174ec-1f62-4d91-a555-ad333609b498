package model

type ExecConfig struct {

	// Attach to `stdin` of the exec command.
	AttachStdin bool `json:"AttachStdin,omitempty"`

	// Attach to `stdout` of the exec command.
	AttachStdout bool `json:"AttachStdout,omitempty"`

	// Attach to `stderr` of the exec command.
	AttachStderr bool `json:"AttachStderr,omitempty"`

	// Override the key sequence for detaching a container. Format is a single character `[a-Z]` or `ctrl-<value>` where `<value>` is one of: `a-z`, `@`, `^`, `[`, `,` or `_`. 
	DetachKeys string `json:"DetachKeys,omitempty"`

	// Allocate a pseudo-TTY.
	Tty bool `json:"Tty,omitempty"`

	// A list of environment variables in the form `[\"VAR=value\", ...]`. 
	Env []string `json:"Env,omitempty"`

	// Command to run, as a string or array of strings.
	Cmd []string `json:"Cmd,omitempty"`

	// Runs the exec process with extended privileges.
	Privileged bool `json:"Privileged,omitempty"`

	// The user, and optionally, group to run the exec process inside the container. Format is one of: `user`, `user:group`, `uid`, or `uid:gid`. 
	User string `json:"User,omitempty"`

	// The working directory for the exec process inside the container. 
	WorkingDir string `json:"WorkingDir,omitempty"`
}
