package middleware

import (
	"net/http"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/service"
)

var casbinService = service.ServiceGroupApp.SystemServiceGroup.CasbinService

// access control拦截器
func CasbinHandler() gin.HandlerFunc {

	return func(c *gin.Context) {

		//获取请求的URI
		obj := c.Request.URL.RequestURI()
		//获取请求方法
		act := c.Request.Method
		//获取用户的角色
		claims := jwt.ExtractClaims(c)
		var sub string
		if v, ok := claims["role"]; ok {
			sub = v.(string)
		}
		//判断策略中是否存在
		e := casbinService.Casbin()
		if ok, _ := e.Enforce(sub, obj, act); ok {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "you don't have permission to access this resource",
			})
		}
	}
}
