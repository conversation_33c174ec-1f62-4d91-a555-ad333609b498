package middleware

import (
	"time"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/model/system"
)

var identityKey = "id"
var username = "username"
var userRole = "role"

func GinJWTMiddlewareInit(jwtAuthorizator IAuthorizator) (authMiddleware *jwt.GinJWTMiddleware) {
	authMiddleware, err := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "test zone",
		Key:         []byte("secret key"),
		Timeout:     time.Minute * 30,
		MaxRefresh:  time.Minute * 30,
		IdentityKey: identityKey,
		SendCookie:  true,
		CookieName:  "jwt",
		PayloadFunc: func(data interface{}) jwt.MapClaims {
			if v, ok := data.(*system.User); ok {
				return jwt.MapClaims{
					username: v.User<PERSON><PERSON>,
					userRole: v.Role,
				}
			}
			return jwt.MapClaims{}
		},
		IdentityHandler: func(c *gin.Context) interface{} {
			claims := jwt.ExtractClaims(c)
			//Set the identity
			return &system.User{
				UserName: claims[username].(string),
				Role:     claims[userRole].(string),
			}
		},
		Authenticator: func(c *gin.Context) (interface{}, error) {
			// handles the login logic. On success LoginResponse is called, on failure Unauthorized is called
			var loginVals system.Auth
			if err := c.ShouldBind(&loginVals); err != nil {
				return "", jwt.ErrMissingLoginValues
			}
			username := loginVals.Username
			password := loginVals.Password
			role := system.GetUserRole(username)
			if system.CheckAuth(username, password) {
				return &system.User{
					UserName: username,
					Role:     role,
				}, nil
			}
			return nil, jwt.ErrFailedAuthentication
		},
		//receives identity and handles authorization logic
		Authorizator: jwtAuthorizator.HandleAuthorizator,
		//handles unauthorized logic
		Unauthorized: func(c *gin.Context, code int, message string) {
			c.JSON(code, gin.H{
				"code":    code,
				"message": message,
			})
		},
		TokenLookup: "header: Authorization, query: token, cookie: jwt",

		TokenHeadName: "Bearer",

		// TimeFunc provides the current time. You can override it to use another time value.
		// This is useful for testing or if your server uses a different time zone than your tokens.
		TimeFunc: time.Now,
	})

	if err != nil {
		global.GvaLog.Fatal("JWT Error:" + err.Error())
	}
	return
}
