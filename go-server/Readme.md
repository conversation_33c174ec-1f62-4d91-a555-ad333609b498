# Docker 容器管理服务平台项目说明

## 项目概述

这是一个基于 Go 语言开发的 Docker 容器管理服务平台，提供了 Docker 容器、镜像、网络等资源的管理功能，通过 Web API 接口方式提供服务。项目采用了现代化的 Web 后端架构，集成了多种中间件和工具，实现了完整的用户认证、授权和资源管理功能。

## 技术栈

- 编程语言 ：Go
- Web 框架 ：Gin
- 数据库 ：SQLite
- ORM 框架 ：GORM
- 日志框架 ：Zap
- 配置管理 ：Viper
- 认证 ：JWT (JSON Web Token)
- 授权 ：Casbin RBAC (基于角色的访问控制)
- API 文档 ：Swagger
- 容器技术 ：Docker、Docker Compose

## 项目结构

```
├── controller/       # 控制器层，处理HTTP请求
├── docs/             # Swagger API文档
├── global/           # 全局变量和常量
├── images/           # 静态图片资源
├── initialize/       # 初始化配置
├── middleware/       # 中间件
├── model/            # 数据模型
│   ├── docker/       # Docker相关模型
│   └── system/       # 系统相关模型
├── router/           # 路由配置
├── service/          # 业务逻辑层
└── utils/            # 工具函数
```

## 核心功能

### 1. 用户管理

- 用户注册、登录、注销
- 用户角色管理（admin、editor、readonly）
- 密码修改
- 用户信息查询

### 2. 权限控制

- 基于 Casbin 的 RBAC 权限模型
- 不同角色拥有不同 API 访问权限：
  - admin：可访问所有 API，支持所有 HTTP 方法
  - editor：可访问/v1/下所有 API，支持所有 HTTP 方法
  - readonly：仅可访问/v1/下所有 API 的 GET 方法

### 3. Docker 资源管理

- 容器管理 ：创建、启动、停止、删除容器，查看容器日志、状态等
- 镜像管理 ：拉取、构建、删除镜像
- 网络管理 ：创建、配置、删除网络
- 卷管理 ：创建、挂载、删除卷
- 服务管理 ：Docker Swarm 服务管理
- 系统管理 ：查看 Docker 系统信息、事件等

### 4. 系统管理

- 系统状态监控
- 主机网络配置
- 系统关机、重启功能

## 开发与部署

### 开发环境

项目提供了 Docker 环境进行开发和测试，可以通过以下命令运行：

```bash
# 创建vendor目录
go mod vendor

# 测试
sudo docker run -it --rm -w "/app" -e "GO111MODULE=on" -e "GOPROXY=off" -e "GIN_MODE=release" -e "TZ=Asia/Shanghai" -v ./:/app  golang:1.23.2-alpine3.20 go test -mod=vendor ./...

# 调试
sudo docker run -it --rm --pid=host --privileged=true -w "/app" -e "air_wd=/app" -e "GIN_MODE=debug" -e "TZ=Asia/Shanghai" -e "HOST_PROC=/host/proc" -v ./:/app -p 8084:8082 -v /proc/:/host/proc/ -v /sys/:/host/sys/ -v air_data:/data -v /var/run/docker.sock:/var/run/docker.sock -v ./docker-compose:/usr/local/bin/docker-compose --name air cosmtrek/air:v1.62.0
```

### 版本信息

- Docker: 24.0.7
- Docker API: 1.43
- Docker Compose: 2.23.3

## 配置说明

项目使用 application.yaml 进行配置：

```yaml
server:
  port: 8082  # 服务端口
  debug: true  # 调试模式

# Casbin配置
casbin:
  modelPath: './model.conf'  # Casbin模型配置文件路径
  policyPath: './policy.csv'  # Casbin策略配置文件路径
```

## 安全特性

- JWT 认证保护 API 访问
- 基于 Casbin 的细粒度权限控制
- 密码加密存储
- CORS 跨域保护

## 总结

该项目是一个功能完善的 Docker 容器管理平台，提供了丰富的 Docker 资源管理功能，并实现了完善的用户认证和授权机制。通过该平台，用户可以方便地管理 Docker 容器、镜像、网络等资源，适用于开发、测试和生产环境中的 Docker 资源管理需求。
