definitions:
  model.Address:
    properties:
      Addr:
        description: IP address.
        type: string
      PrefixLen:
        description: Mask length of the IP address.
        type: integer
    type: object
  model.ContainerConfig:
    properties:
      ArgsEscaped:
        description: Command is already escaped (Windows only)
        type: boolean
      AttachStderr:
        description: Whether to attach to `stderr`.
        type: boolean
      AttachStdin:
        description: Whether to attach to `stdin`.
        type: boolean
      AttachStdout:
        description: Whether to attach to `stdout`.
        type: boolean
      Cmd:
        description: Command to run specified as a string or an array of strings.
        items:
          type: string
        type: array
      Domainname:
        description: The domain name to use for the container.
        type: string
      Entrypoint:
        description: The entry point for the container as a string or an array of
          strings.  If the array consists of exactly one empty string (`[\"\"]`) then
          the entry point is reset to system default (i.e., the entry point used by
          docker when there is no `ENTRYPOINT` instruction in the `Dockerfile`).
        items:
          type: string
        type: array
      Env:
        description: A list of environment variables to set inside the container in
          the form `[\"VAR=value\", ...]`. A variable without `=` is removed from
          the environment, rather than to have an empty value.
        items:
          type: string
        type: array
      ExposedPorts:
        additionalProperties:
          additionalProperties: true
          type: object
        description: 'An object mapping ports to an empty object in the form:  `{\"<port>/<tcp|udp|sctp>\":
          {}}`'
        type: object
      Healthcheck:
        $ref: '#/definitions/model.HealthConfig'
      Hostname:
        description: The hostname to use for the container, as a valid RFC 1123 hostname.
        type: string
      Image:
        description: The name of the image to use when creating the container/
        type: string
      Labels:
        additionalProperties:
          type: string
        description: User-defined key/value metadata.
        type: object
      MacAddress:
        description: MAC address of the container.
        type: string
      NetworkDisabled:
        description: Disable networking for the container.
        type: boolean
      OnBuild:
        description: '`ONBUILD` metadata that were defined in the image''s `Dockerfile`.'
        items:
          type: string
        type: array
      OpenStdin:
        description: Open `stdin`
        type: boolean
      Shell:
        description: Shell for when `RUN`, `CMD`, and `ENTRYPOINT` uses a shell.
        items:
          type: string
        type: array
      StdinOnce:
        description: Close `stdin` after one attached client disconnects
        type: boolean
      StopSignal:
        description: Signal to stop a container as a string or unsigned integer.
        type: string
      StopTimeout:
        description: Timeout to stop a container in seconds.
        type: integer
      Tty:
        description: Attach standard streams to a TTY, including `stdin` if it is
          not closed.
        type: boolean
      User:
        description: The user that commands are run as inside the container.
        type: string
      Volumes:
        additionalProperties:
          additionalProperties: true
          type: object
        description: An object mapping mount point paths inside the container to empty
          objects.
        type: object
      WorkingDir:
        description: The working directory for commands to run in.
        type: string
    type: object
  model.ContainerInspectResponse:
    properties:
      AppArmorProfile:
        type: string
      Args:
        description: The arguments to the command being run
        items:
          type: string
        type: array
      Config:
        $ref: '#/definitions/model.ContainerConfig'
      Created:
        description: The time the container was created
        type: string
      Driver:
        type: string
      ExecIDs:
        description: IDs of exec instances that are running in the container.
        items:
          type: string
        type: array
      GraphDriver:
        $ref: '#/definitions/model.GraphDriverData'
      HostConfig:
        $ref: '#/definitions/model.HostConfig'
      HostnamePath:
        type: string
      HostsPath:
        type: string
      Id:
        description: The ID of the container
        type: string
      Image:
        description: The container's image ID
        type: string
      LogPath:
        type: string
      MountLabel:
        type: string
      Mounts:
        items:
          $ref: '#/definitions/model.MountPoint'
        type: array
      Name:
        type: string
      NetworkSettings:
        $ref: '#/definitions/model.NetworkSettings'
      Path:
        description: The path to the command being run
        type: string
      Platform:
        type: string
      ProcessLabel:
        type: string
      ResolvConfPath:
        type: string
      RestartCount:
        type: integer
      SizeRootFs:
        description: The total size of all the files in this container.
        type: integer
      SizeRw:
        description: The size of files that have been created or changed by this container.
        type: integer
      State:
        $ref: '#/definitions/model.ContainerState'
    type: object
  model.ContainerM:
    properties:
      address:
        type: string
      created:
        type: string
      id:
        type: string
      image:
        type: string
      name:
        type: string
      network:
        type: string
      restartpolicy:
        type: string
      status:
        type: string
      volume:
        type: string
    type: object
  model.ContainerNetwork:
    properties:
      containerID:
        type: string
      ipaddress:
        type: string
      networkID:
        type: string
    type: object
  model.ContainerState:
    properties:
      Dead:
        type: boolean
      Error:
        type: string
      ExitCode:
        description: The last exit code of this container
        type: integer
      FinishedAt:
        description: The time when this container last exited.
        type: string
      Health:
        $ref: '#/definitions/model.Health'
      OOMKilled:
        description: Whether this container has been killed because it ran out of
          memory.
        type: boolean
      Paused:
        description: Whether this container is paused.
        type: boolean
      Pid:
        description: The process ID of this container
        type: integer
      Restarting:
        description: Whether this container is restarting.
        type: boolean
      Running:
        description: 'Whether this container is running.  Note that a running container
          can be _paused_. The `Running` and `Paused` booleans are not mutually exclusive:  When
          pausing a container (on Linux), the freezer cgroup is used to suspend all
          processes in the container. Freezing the process requires the process to
          be running. As a result, paused containers are both `Running` _and_ `Paused`.  Use
          the `Status` field instead to determine if a container''s state is \"running\".'
        type: boolean
      StartedAt:
        description: The time when this container was last started.
        type: string
      Status:
        description: String representation of the container state. Can be one of \"created\",
          \"running\", \"paused\", \"restarting\", \"removing\", \"exited\", or \"dead\".
        type: string
    type: object
  model.EndpointIpamConfig:
    properties:
      IPv4Address:
        type: string
      IPv6Address:
        type: string
      LinkLocalIPs:
        items:
          type: string
        type: array
    type: object
  model.EndpointSettings:
    properties:
      Aliases:
        items:
          type: string
        type: array
      DriverOpts:
        additionalProperties:
          type: string
        description: DriverOpts is a mapping of driver options and values. These options
          are passed directly to the driver and are driver specific.
        type: object
      EndpointID:
        description: Unique ID for the service endpoint in a Sandbox.
        type: string
      Gateway:
        description: Gateway address for this network.
        type: string
      GlobalIPv6Address:
        description: Global IPv6 address.
        type: string
      GlobalIPv6PrefixLen:
        description: Mask length of the global IPv6 address.
        type: integer
      IPAMConfig:
        $ref: '#/definitions/model.EndpointIpamConfig'
      IPAddress:
        description: IPv4 address.
        type: string
      IPPrefixLen:
        description: Mask length of the IPv4 address.
        type: integer
      IPv6Gateway:
        description: IPv6 gateway address.
        type: string
      Links:
        items:
          type: string
        type: array
      MacAddress:
        description: MAC address for the endpoint on this network.
        type: string
      NetworkID:
        description: Unique ID of the network.
        type: string
    type: object
  model.Entrypoints:
    properties:
      attrs:
        additionalProperties:
          type: string
        type: object
      ip:
        type: string
      name:
        type: string
      path:
        type: string
      port:
        type: integer
      tags:
        items:
          type: string
        type: array
    type: object
  model.GraphDriverData:
    properties:
      Data:
        additionalProperties:
          type: string
        type: object
      Name:
        type: string
    type: object
  model.Health:
    properties:
      FailingStreak:
        description: FailingStreak is the number of consecutive failures
        type: integer
      Log:
        description: Log contains the last few results (oldest first)
        items:
          $ref: '#/definitions/model.HealthcheckResult'
        type: array
      Status:
        description: Status is one of `none`, `starting`, `healthy` or `unhealthy`  -
          \"none\"      Indicates there is no healthcheck - \"starting\"  Starting
          indicates that the container is not yet ready - \"healthy\"   Healthy indicates
          that the container is running correctly - \"unhealthy\" Unhealthy indicates
          that the container has a problem
        type: string
    type: object
  model.HealthConfig:
    properties:
      Interval:
        description: The time to wait between checks in nanoseconds. It should be
          0 or at least 1000000 (1 ms). 0 means inherit.
        type: integer
      Retries:
        description: The number of consecutive failures needed to consider a container
          as unhealthy. 0 means inherit.
        type: integer
      StartPeriod:
        description: Start period for the container to initialize before starting
          health-retries countdown in nanoseconds. It should be 0 or at least 1000000
          (1 ms). 0 means inherit.
        type: integer
      Test:
        description: 'The test to perform. Possible values are:  - `[]` inherit healthcheck
          from image or parent image - `[\"NONE\"]` disable healthcheck - `[\"CMD\",
          args...]` exec arguments directly - `[\"CMD-SHELL\", command]` run command
          with system''s default shell'
        items:
          type: string
        type: array
      Timeout:
        description: The time to wait before considering the check to have hung. It
          should be 0 or at least 1000000 (1 ms). 0 means inherit.
        type: integer
    type: object
  model.HealthcheckResult:
    properties:
      End:
        description: Date and time at which this check ended in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt)
          format with nano-seconds.
        type: string
      ExitCode:
        description: 'ExitCode meanings:  - `0` healthy - `1` unhealthy - `2` reserved
          (considered unhealthy) - other values: error running probe'
        type: integer
      Output:
        description: Output from last check
        type: string
      Start:
        description: Date and time at which this check started in [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt)
          format with nano-seconds.
        type: string
    type: object
  model.HostConfig:
    properties:
      AutoRemove:
        description: Automatically remove the container when the container's process
          exits. This has no effect if `RestartPolicy` is set.
        type: boolean
      Binds:
        description: 'A list of volume bindings for this container. Each volume binding
          is a string in one of these forms:  - `host-src:container-dest[:options]`
          to bind-mount a host path   into the container. Both `host-src`, and `container-dest`
          must   be an _absolute_ path. - `volume-name:container-dest[:options]` to
          bind-mount a volume   managed by a volume driver into the container. `container-dest`   must
          be an _absolute_ path.  `options` is an optional, comma-delimited list of:  -
          `nocopy` disables automatic copying of data from the container   path to
          the volume. The `nocopy` flag only applies to named volumes. - `[ro|rw]`
          mounts a volume read-only or read-write, respectively.   If omitted or set
          to `rw`, volumes are mounted read-write. - `[z|Z]` applies SELinux labels
          to allow or deny multiple containers   to read and write to the same volume.     -
          `z`: a _shared_ content label is applied to the content. This       label
          indicates that multiple containers can share the volume       content, for
          both reading and writing.     - `Z`: a _private unshared_ label is applied
          to the content.       This label indicates that only the current container
          can use       a private volume. Labeling systems such as SELinux require       proper
          labels to be placed on volume content that is mounted       into a container.
          Without a label, the security system can       prevent a container''s processes
          from using the content. By       default, the labels set by the host operating
          system are not       modified. - `[[r]shared|[r]slave|[r]private]` specifies
          mount   [propagation behavior](https://www.kernel.org/doc/Documentation/filesystems/sharedsubtree.txt).   This
          only applies to bind-mounted volumes, not internal volumes   or named volumes.
          Mount propagation requires the source mount   point (the location where
          the source directory is mounted in the   host operating system) to have
          the correct propagation properties.   For shared volumes, the source mount
          point must be set to `shared`.   For slave volumes, the mount must be set
          to either `shared` or   `slave`.'
        items:
          type: string
        type: array
      CapAdd:
        description: A list of kernel capabilities to add to the container. Conflicts
          with option 'Capabilities'.
        items:
          type: string
        type: array
      CapDrop:
        description: A list of kernel capabilities to drop from the container. Conflicts
          with option 'Capabilities'.
        items:
          type: string
        type: array
      Cgroup:
        description: Cgroup to use for the container.
        type: string
      CgroupnsMode:
        description: 'cgroup namespace mode for the container. Possible values are:  -
          `\"private\"`: the container runs in its own private cgroup namespace -
          `\"host\"`: use the host system''s cgroup namespace  If not specified, the
          daemon default is used, which can either be `\"private\"` or `\"host\"`,
          depending on daemon version, kernel support and configuration.'
        type: string
      ConsoleSize:
        description: Initial console size, as an `[height, width]` array. (Windows
          only)
        items:
          type: integer
        type: array
      ContainerIDFile:
        description: Path to a file where the container ID is written
        type: string
      Dns:
        description: A list of DNS servers for the container to use.
        items:
          type: string
        type: array
      DnsOptions:
        description: A list of DNS options.
        items:
          type: string
        type: array
      DnsSearch:
        description: A list of DNS search domains.
        items:
          type: string
        type: array
      ExtraHosts:
        description: A list of hostnames/IP mappings to add to the container's `/etc/hosts`
          file. Specified in the form `[\"hostname:IP\"]`.
        items:
          type: string
        type: array
      GroupAdd:
        description: A list of additional groups that the container process will run
          as.
        items:
          type: string
        type: array
      IpcMode:
        description: 'IPC sharing mode for the container. Possible values are:  -
          `\"none\"`: own private IPC namespace, with /dev/shm not mounted - `\"private\"`:
          own private IPC namespace - `\"shareable\"`: own private IPC namespace,
          with a possibility to share it with other containers - `\"container:<name|id>\"`:
          join another (shareable) container''s IPC namespace - `\"host\"`: use the
          host system''s IPC namespace  If not specified, daemon default is used,
          which can either be `\"private\"` or `\"shareable\"`, depending on daemon
          version and configuration.'
        type: string
      Isolation:
        description: Isolation technology of the container. (Windows only)
        type: string
      Links:
        description: A list of links for the container in the form `container_name:alias`.
        items:
          type: string
        type: array
      LogConfig:
        $ref: '#/definitions/model.HostConfigAllOfLogConfig'
      MaskedPaths:
        description: The list of paths to be masked inside the container (this overrides
          the default set of paths).
        items:
          type: string
        type: array
      Mounts:
        description: Specification for mounts to be added to the container.
        items:
          $ref: '#/definitions/model.Mount'
        type: array
      NetworkMode:
        description: 'Network mode to use for this container. Supported standard values
          are: `bridge`, `host`, `none`, and `container:<name|id>`. Any other value
          is taken as a custom network''s name to which this container should connect
          to.'
        type: string
      OomScoreAdj:
        description: An integer value containing the score given to the container
          in order to tune OOM killer preferences.
        type: integer
      PidMode:
        description: 'Set the PID (Process) Namespace mode for the container. It can
          be either:  - `\"container:<name|id>\"`: joins another container''s PID
          namespace - `\"host\"`: use the host''s PID namespace inside the container'
        type: string
      PortBindings:
        additionalProperties:
          items:
            $ref: '#/definitions/model.PortBinding'
          type: array
        description: PortMap describes the mapping of container ports to host ports,
          using the container's port-number and protocol as key in the format `<port>/<protocol>`,
          for example, `80/udp`.  If a container's port is mapped for multiple protocols,
          separate entries are added to the mapping table.
        type: object
      Privileged:
        description: Gives the container full access to the host.
        type: boolean
      PublishAllPorts:
        description: Allocates an ephemeral host port for all of a container's exposed
          ports.  Ports are de-allocated when the container stops and allocated when
          the container starts. The allocated port might be changed when restarting
          the container.  The port is selected from the ephemeral port range that
          depends on the kernel. For example, on Linux the range is defined by `/proc/sys/net/ipv4/ip_local_port_range`.
        type: boolean
      ReadonlyPaths:
        description: The list of paths to be set as read-only inside the container
          (this overrides the default set of paths).
        items:
          type: string
        type: array
      ReadonlyRootfs:
        description: Mount the container's root filesystem as read only.
        type: boolean
      RestartPolicy:
        $ref: '#/definitions/model.RestartPolicy'
      Runtime:
        description: Runtime to use with this container.
        type: string
      SecurityOpt:
        description: A list of string values to customize labels for MLS systems,
          such as SELinux.
        items:
          type: string
        type: array
      ShmSize:
        description: Size of `/dev/shm` in bytes. If omitted, the system uses 64MB.
        type: integer
      StorageOpt:
        additionalProperties:
          type: string
        description: 'Storage driver options for this container, in the form `{\"size\":
          \"120G\"}`.'
        type: object
      Sysctls:
        additionalProperties:
          type: string
        description: 'A list of kernel parameters (sysctls) to set in the container.
          For example:  ``` {\"net.ipv4.ip_forward\": \"1\"} ```'
        type: object
      Tmpfs:
        additionalProperties:
          type: string
        description: 'A map of container directories which should be replaced by tmpfs
          mounts, and their corresponding mount options. For example:  ``` { \"/run\":
          \"rw,noexec,nosuid,size=65536k\" } ```'
        type: object
      UTSMode:
        description: UTS namespace to use for the container.
        type: string
      UsernsMode:
        description: Sets the usernamespace mode for the container when usernamespace
          remapping option is enabled.
        type: string
      VolumeDriver:
        description: Driver that this container uses to mount volumes.
        type: string
      VolumesFrom:
        description: A list of volumes to inherit from another container, specified
          in the form `<container name>[:<ro|rw>]`.
        items:
          type: string
        type: array
    type: object
  model.HostConfigAllOfLogConfig:
    properties:
      Config:
        additionalProperties:
          type: string
        type: object
      Type:
        type: string
    type: object
  model.Mount:
    properties:
      BindOptions:
        $ref: '#/definitions/model.MountBindOptions'
      Consistency:
        description: 'The consistency requirement for the mount: `default`, `consistent`,
          `cached`, or `delegated`.'
        type: string
      ReadOnly:
        description: Whether the mount should be read-only.
        type: boolean
      Source:
        description: Mount source (e.g. a volume name, a host path).
        type: string
      Target:
        description: Container path.
        type: string
      TmpfsOptions:
        $ref: '#/definitions/model.MountTmpfsOptions'
      Type:
        description: 'The mount type. Available types:  - `bind` Mounts a file or
          directory from the host into the container. Must exist prior to creating
          the container. - `volume` Creates a volume with the given name and options
          (or uses a pre-existing volume with the same name and options). These are
          **not** removed when the container is removed. - `tmpfs` Create a tmpfs
          with the given options. The mount source cannot be specified for tmpfs.
          - `npipe` Mounts a named pipe from the host into the container. Must exist
          prior to creating the container.'
        type: string
      VolumeOptions:
        $ref: '#/definitions/model.MountVolumeOptions'
    type: object
  model.MountBindOptions:
    properties:
      NonRecursive:
        description: Disable recursive bind mount.
        type: boolean
      Propagation:
        description: A propagation mode with the value `[r]private`, `[r]shared`,
          or `[r]slave`.
        type: string
    type: object
  model.MountPoint:
    properties:
      Destination:
        type: string
      Driver:
        type: string
      Mode:
        type: string
      Name:
        type: string
      Propagation:
        type: string
      RW:
        type: boolean
      Source:
        type: string
      Type:
        type: string
    type: object
  model.MountTmpfsOptions:
    properties:
      Mode:
        description: The permission mode for the tmpfs mount in an integer.
        type: integer
      SizeBytes:
        description: The size for the tmpfs mount in bytes.
        type: integer
    type: object
  model.MountVolumeOptions:
    properties:
      DriverConfig:
        $ref: '#/definitions/model.MountVolumeOptionsDriverConfig'
      Labels:
        additionalProperties:
          type: string
        description: User-defined key/value metadata.
        type: object
      NoCopy:
        description: Populate volume with data from the target.
        type: boolean
    type: object
  model.MountVolumeOptionsDriverConfig:
    properties:
      Name:
        description: Name of the driver to use to create the volume.
        type: string
      Options:
        additionalProperties:
          type: string
        description: key/value map of driver specific options.
        type: object
    type: object
  model.Network:
    properties:
      driver:
        type: string
      gateway:
        type: string
      id:
        type: string
      name:
        type: string
      parent:
        type: string
      subnet:
        type: string
    type: object
  model.NetworkConnectRequest:
    properties:
      containerID:
        type: string
      ipaddress:
        type: string
    type: object
  model.NetworkCreateRequest:
    properties:
      checkDuplicate:
        type: boolean
      driver:
        type: string
      gateway:
        type: string
      name:
        type: string
      parent:
        type: string
      subnet:
        type: string
    type: object
  model.NetworkDisconnectRequest:
    properties:
      containerID:
        description: The ID or name of the container to connect to the network.
        type: string
      force:
        type: boolean
    type: object
  model.NetworkSettings:
    properties:
      Bridge:
        description: Name of the network'a bridge (for example, `docker0`).
        type: string
      EndpointID:
        description: 'EndpointID uniquely represents a service endpoint in a Sandbox.  <p><br
          /></p>  > **Deprecated**: This field is only propagated when attached to
          the > default \"bridge\" network. Use the information from the \"bridge\"
          > network inside the `Networks` map instead, which contains the same > information.
          This field was deprecated in Docker 1.9 and is scheduled > to be removed
          in Docker 17.12.0'
        type: string
      Gateway:
        description: 'Gateway address for the default \"bridge\" network.  <p><br
          /></p>  > **Deprecated**: This field is only propagated when attached to
          the > default \"bridge\" network. Use the information from the \"bridge\"
          > network inside the `Networks` map instead, which contains the same > information.
          This field was deprecated in Docker 1.9 and is scheduled > to be removed
          in Docker 17.12.0'
        type: string
      GlobalIPv6Address:
        description: 'Global IPv6 address for the default \"bridge\" network.  <p><br
          /></p>  > **Deprecated**: This field is only propagated when attached to
          the > default \"bridge\" network. Use the information from the \"bridge\"
          > network inside the `Networks` map instead, which contains the same > information.
          This field was deprecated in Docker 1.9 and is scheduled > to be removed
          in Docker 17.12.0'
        type: string
      GlobalIPv6PrefixLen:
        description: 'Mask length of the global IPv6 address.  <p><br /></p>  > **Deprecated**:
          This field is only propagated when attached to the > default \"bridge\"
          network. Use the information from the \"bridge\" > network inside the `Networks`
          map instead, which contains the same > information. This field was deprecated
          in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0'
        type: integer
      HairpinMode:
        description: Indicates if hairpin NAT should be enabled on the virtual interface.
        type: boolean
      IPAddress:
        description: 'IPv4 address for the default \"bridge\" network.  <p><br /></p>  >
          **Deprecated**: This field is only propagated when attached to the > default
          \"bridge\" network. Use the information from the \"bridge\" > network inside
          the `Networks` map instead, which contains the same > information. This
          field was deprecated in Docker 1.9 and is scheduled > to be removed in Docker
          17.12.0'
        type: string
      IPPrefixLen:
        description: 'Mask length of the IPv4 address.  <p><br /></p>  > **Deprecated**:
          This field is only propagated when attached to the > default \"bridge\"
          network. Use the information from the \"bridge\" > network inside the `Networks`
          map instead, which contains the same > information. This field was deprecated
          in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0'
        type: integer
      IPv6Gateway:
        description: 'IPv6 gateway address for this network.  <p><br /></p>  > **Deprecated**:
          This field is only propagated when attached to the > default \"bridge\"
          network. Use the information from the \"bridge\" > network inside the `Networks`
          map instead, which contains the same > information. This field was deprecated
          in Docker 1.9 and is scheduled > to be removed in Docker 17.12.0'
        type: string
      LinkLocalIPv6Address:
        description: IPv6 unicast address using the link-local prefix.
        type: string
      LinkLocalIPv6PrefixLen:
        description: Prefix length of the IPv6 unicast address.
        type: integer
      MacAddress:
        description: 'MAC address for the container on the default \"bridge\" network.  <p><br
          /></p>  > **Deprecated**: This field is only propagated when attached to
          the > default \"bridge\" network. Use the information from the \"bridge\"
          > network inside the `Networks` map instead, which contains the same > information.
          This field was deprecated in Docker 1.9 and is scheduled > to be removed
          in Docker 17.12.0'
        type: string
      Networks:
        additionalProperties:
          $ref: '#/definitions/model.EndpointSettings'
        description: Information about all networks that the container is connected
          to.
        type: object
      Ports:
        additionalProperties:
          items:
            $ref: '#/definitions/model.PortBinding'
          type: array
        description: PortMap describes the mapping of container ports to host ports,
          using the container's port-number and protocol as key in the format `<port>/<protocol>`,
          for example, `80/udp`.  If a container's port is mapped for multiple protocols,
          separate entries are added to the mapping table.
        type: object
      SandboxID:
        description: SandboxID uniquely represents a container's network stack.
        type: string
      SandboxKey:
        description: SandboxKey identifies the sandbox
        type: string
      SecondaryIPAddresses:
        items:
          $ref: '#/definitions/model.Address'
        type: array
      SecondaryIPv6Addresses:
        items:
          $ref: '#/definitions/model.Address'
        type: array
    type: object
  model.PortBinding:
    properties:
      HostIp:
        description: Host IP address that the container's port is mapped to.
        type: string
      HostPort:
        description: Host port number that the container's port is mapped to.
        type: string
    type: object
  model.RestartPolicy:
    properties:
      MaximumRetryCount:
        description: If `on-failure` is used, the number of times to retry before
          giving up.
        type: integer
      Name:
        description: '- Empty string means not to restart - `no` Do not automatically
          restart - `always` Always restart - `unless-stopped` Restart always except
          when the user has manually stopped the container - `on-failure` Restart
          only when the container exit code is non-zero'
        type: string
    type: object
  model.Volume:
    properties:
      CreatedAt:
        description: Date/Time the volume was created.
        type: string
      Driver:
        description: Name of the volume driver used by the volume.
        type: string
      Labels:
        additionalProperties:
          type: string
        description: User-defined key/value metadata.
        type: object
      Mountpoint:
        description: Mount path of the volume on the host.
        type: string
      Name:
        description: Name of the volume.
        type: string
      Options:
        additionalProperties:
          type: string
        description: The driver specific options used when creating the volume.
        type: object
      Scope:
        description: The level at which the volume exists. Either `global` for cluster-wide,
          or `local` for machine level.
        type: string
      Status:
        additionalProperties:
          additionalProperties: true
          type: object
        description: 'Low-level details about the volume, provided by the volume driver.
          Details are returned as a map with key/value pairs: `{\"key\":\"value\",\"key2\":\"value2\"}`.  The
          `Status` field is optional, and is omitted if the volume driver does not
          support this feature.'
        type: object
      UsageData:
        $ref: '#/definitions/model.VolumeUsageData'
    type: object
  model.VolumeListResponse:
    properties:
      Volumes:
        description: List of volumes
        items:
          $ref: '#/definitions/model.Volume'
        type: array
      Warnings:
        description: Warnings that occurred when fetching the list of volumes.
        items:
          type: string
        type: array
    type: object
  model.VolumePruneResponse:
    properties:
      SpaceReclaimed:
        description: Disk space reclaimed in bytes
        type: integer
      VolumesDeleted:
        description: Volumes that were deleted
        items:
          type: string
        type: array
    type: object
  model.VolumeUsageData:
    properties:
      RefCount:
        description: The number of containers referencing this volume. This field
          is set to `-1` if the reference-count is not available.
        type: integer
      Size:
        description: Amount of disk space used by the volume (in bytes). This information
          is only available for volumes created with the `\"local\"` volume driver.
          For volumes created with other volume drivers, this field is set to `-1`
          (\"not available\")
        type: integer
    type: object
  openapi.ImageM:
    properties:
      created:
        type: string
      id:
        type: string
      size:
        type: integer
      status:
        type: string
      tag:
        items:
          type: string
        type: array
    type: object
info:
  contact: {}
paths:
  /about:
    get:
      description: 关于
      produces:
      - application/json
      responses: {}
      summary: 关于
      tags:
      - platform
  /v1//volumes/create:
    post:
      description: 创建挂载卷
      produces:
      - application/json
      responses:
        "200":
          description: Create a volume
          schema:
            $ref: '#/definitions/model.Volume'
      summary: 创建挂载卷
      tags:
      - volume
  /v1/containers/{id}:
    delete:
      description: 删除容器
      produces:
      - application/json
      responses: {}
      summary: 删除容器
      tags:
      - container
  /v1/containers/{id}/json:
    get:
      description: 容器信息
      produces:
      - application/json
      responses:
        "200":
          description: Inspect a container
          schema:
            $ref: '#/definitions/model.ContainerInspectResponse'
      summary: 容器信息
      tags:
      - container
  /v1/containers/{id}/kill:
    post:
      description: 杀死容器进程
      produces:
      - application/json
      responses: {}
      summary: 杀死容器进程
      tags:
      - container
  /v1/containers/{id}/network:
    get:
      description: 容器网络信息
      produces:
      - application/json
      responses:
        "200":
          description: Container Network
          schema:
            items:
              $ref: '#/definitions/model.ContainerNetwork'
            type: array
      summary: 容器网络信息
      tags:
      - container
  /v1/containers/{id}/restart:
    post:
      description: 重启容器
      produces:
      - application/json
      responses: {}
      summary: 重启容器
      tags:
      - container
  /v1/containers/{id}/start:
    post:
      description: 启动容器
      produces:
      - application/json
      responses: {}
      summary: 启动容器
      tags:
      - container
  /v1/containers/{id}/stats:
    get:
      description: 容器状态
      produces:
      - application/json
      responses: {}
      summary: 容器状态
      tags:
      - container
  /v1/containers/{id}/stop:
    post:
      description: 停止容器
      produces:
      - application/json
      responses: {}
      summary: 停止容器
      tags:
      - container
  /v1/containers/create:
    post:
      description: 创建容器
      produces:
      - application/json
      responses: {}
      summary: 创建容器
      tags:
      - container
  /v1/containers/entrypoints:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: Entrypoints list
          schema:
            items:
              $ref: '#/definitions/model.Entrypoints'
            type: array
      summary: 获取所有服务入口
      tags:
      - container
  /v1/containers/json:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: Containers list
          schema:
            items:
              $ref: '#/definitions/model.ContainerM'
            type: array
      summary: 获取所有容器列表(包括未启动)
      tags:
      - container
  /v1/images/{name}:
    delete:
      description: 删除镜像
      produces:
      - application/json
      responses: {}
      summary: 删除镜像
      tags:
      - image
  /v1/images/json:
    get:
      description: 获取所有镜像
      produces:
      - application/json
      responses:
        "200":
          description: Images list
          schema:
            items:
              $ref: '#/definitions/openapi.ImageM'
            type: array
      summary: 获取所有镜像
      tags:
      - image
  /v1/images/load:
    post:
      description: 导入镜像
      produces:
      - application/json
      responses: {}
      summary: 导入镜像
      tags:
      - image
  /v1/networks:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: networks list
          schema:
            items:
              $ref: '#/definitions/model.Network'
            type: array
      summary: 获取网络列表
      tags:
      - network
  /v1/networks/{id}:
    delete:
      parameters:
      - description: network id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 删除网络
      tags:
      - network
  /v1/networks/{id}/connect:
    post:
      parameters:
      - description: network id
        in: path
        name: id
        required: true
        type: string
      - description: Network Connect Request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.NetworkConnectRequest'
      produces:
      - application/json
      responses: {}
      summary: 将容器连接到网络中
      tags:
      - network
  /v1/networks/{id}/disconnect:
    post:
      parameters:
      - description: network id
        in: path
        name: id
        required: true
        type: string
      - description: Network Disconnect Request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.NetworkDisconnectRequest'
      produces:
      - application/json
      responses: {}
      summary: 断开一个容器与网络的连接
      tags:
      - network
  /v1/networks/create:
    post:
      parameters:
      - description: Network Create Request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.NetworkCreateRequest'
      produces:
      - application/json
      responses: {}
      summary: 创建网络
      tags:
      - network
  /v1/networks/interfaces:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: Interfaces list
          schema:
            items:
              additionalProperties: true
              type: object
            type: array
      summary: 获取网卡
      tags:
      - network
  /v1/platform/reboot:
    post:
      description: 重启服务平台,仅在容器化时才可用
      produces:
      - application/json
      responses: {}
      summary: 重启服务平台
      tags:
      - platform
  /v1/platform/update:
    post:
      description: 更新服务平台,仅在容器化时才可用
      produces:
      - application/json
      responses: {}
      summary: 更新服务平台
      tags:
      - platform
  /v1/volumes:
    get:
      description: 挂载卷列表
      produces:
      - application/json
      responses:
        "200":
          description: Volume List
          schema:
            $ref: '#/definitions/model.VolumeListResponse'
      summary: 获取所有挂载卷列表
      tags:
      - volume
  /v1/volumes/{name}:
    delete:
      description: 删除挂载卷
      produces:
      - application/json
      responses: {}
      summary: 删除挂载卷
      tags:
      - volume
    get:
      description: 挂载卷信息
      produces:
      - application/json
      responses:
        "200":
          description: Inspect a volume
          schema:
            $ref: '#/definitions/model.Volume'
      summary: 挂载卷信息
      tags:
      - volume
  /v1/volumes/prune:
    post:
      description: 删除未使用的卷
      produces:
      - application/json
      responses:
        "200":
          description: Delete unused volumes
          schema:
            $ref: '#/definitions/model.VolumePruneResponse'
      summary: 删除未使用的卷
      tags:
      - volume
swagger: "2.0"
