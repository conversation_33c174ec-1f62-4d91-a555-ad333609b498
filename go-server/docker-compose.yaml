services:
  go-server:
    image: cosmtrek/air:v1.62.0
    environment:
      - air_wd=/app
      - GIN_MODE=debug
      - TZ=Asia/Shanghai
      - HOST_PROC=/host/proc
    pid: host
    privileged: true
    working_dir: /app
    volumes:
      - .:/app
      - /proc/:/host/proc/
      - /sys/:/host/sys/
      - /etc/network/interfaces.d/:/host/interfaces/
      - air_data:/data
      - /var/run/docker.sock:/var/run/docker.sock
      - ./docker-compose-linux-x86_64:/usr/local/bin/docker-compose
    restart: always
    container_name: air
    ports:
      - "8084:8082"

volumes:
  air_data: