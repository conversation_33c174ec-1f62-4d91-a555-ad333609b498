package system

import (
	"sync"

	"github.com/casbin/casbin/v2"
	"github.com/spf13/viper"
	_ "github.com/glebarez/sqlite"
)

type CasbinService struct{}

var CasbinServiceApp = new(CasbinService)

var (
	enforcer *casbin.Enforcer
	once     sync.Once
)

func (casbinService *CasbinService) Casbin() *casbin.Enforcer {
	once.Do(func() {
		modelPath := viper.GetString("casbin.modelPath")
		policyPath := viper.GetString("casbin.policyPath")
		enforcer, _ = casbin.NewEnforcer(modelPath, policyPath)
	})
	_ = enforcer.LoadPolicy()
	return enforcer
}
