package router

import (
	"net/http"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"mediacomm.com/skylink/controller"
	docs "mediacomm.com/skylink/docs"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/middleware"
)

// Route is the information for every URI.
type Route struct {
	// Name is the name of this Route.
	Name string
	// Method is the string for the HTTP method. ex) GET, POST etc..
	Method string
	// Pattern is the pattern of the URI.
	Pattern string
	// HandlerFunc is the handler function of this route.
	HandlerFunc gin.HandlerFunc
}

// Routes is the list of the generated Route.
type Routes []Route

// NewRouter returns a new router.
func NewRouter() *gin.Engine {
	engin := gin.New()
	pprof.Register(engin)
	engin.Use(gin.Logger())
	engin.Use(gin.Recovery())
	engin.Use(middleware.CORS)
	engin.StaticFS("/imgs", http.Dir("./images"))

	engin.GET("/ping", func(ctx *gin.Context) { ctx.JSON(http.StatusOK, gin.H{"message": "pong"}) })
	if gin.Mode() == gin.DebugMode {
		docs.SwaggerInfo.BasePath = "/v1"
		docs.SwaggerInfo.Title = "go-server API"
		engin.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))
	}
	engin.GET("/gping", controller.GpingHandle)

	allUserMiddleware := middleware.GinJWTMiddlewareInit(&middleware.AllUserAuthorizator{})
	engin.GET("/about", controller.PlatformAbout)
	engin.POST("/login", allUserMiddleware.LoginHandler)
	engin.POST("/logout", allUserMiddleware.LogoutHandler)
	// 404 handler
	engin.NoRoute(allUserMiddleware.MiddlewareFunc(), func(c *gin.Context) {
		claims := jwt.ExtractClaims(c)
		global.GvaLog.Errorf("NoRoute claims: %#v\n", claims)
		c.JSON(404, gin.H{"code": "PAGE_NOT_FOUND", "message": "Page not found"})
	})
	auth := engin.Group("/auth")
	auth.GET("/refresh_token", allUserMiddleware.RefreshHandler)

	engin.Use(allUserMiddleware.MiddlewareFunc())
	// 所有用户可以查询用户信息
	engin.GET("/user/:username/info", controller.UserInfo)
	engin.Use(middleware.CasbinHandler())
	api := engin.Group("/user")
	{
		api.GET("/info", controller.UsersInfo)
		// api.GET("/:username/info", controller.UserInfo)
		api.POST("/register", controller.UserRegister)
		api.POST("/change_password", controller.UserChangePassword)
		api.POST("/change_role", controller.UserChangeRole)
		api.POST("/change_comment", controller.UserChangeComment)
		api.DELETE("/delete_user", controller.UserDelete)
	}
	sysGroup := engin.Group("/system")
	{
		sysGroup.GET("/info", controller.GetSystemStatusInfo)
		sysGroup.POST("/change_system_ip", controller.ChangeHostNetwork)
		sysGroup.POST("/shutdown", controller.ShutdownHost)
		sysGroup.POST("/reboot", controller.RebootHost)
	}
	groupV1 := engin.Group("v1")
	{

		groupV1.GET("/version", controller.SystemVersion)

		configsRouter := groupV1.Group("/configs")
		setRoutes(configsRouter, &configsRoutes)

		containersRouter := groupV1.Group("/containers")
		setRoutes(containersRouter, &containersRoutes)

		execRouter := groupV1.Group("/exec")
		setRoutes(execRouter, &execRoutes)

		buildRouter := groupV1.Group("/build")
		setRoutes(buildRouter, &buildRoutes)

		imagesRouter := groupV1.Group("/images")
		setRoutes(imagesRouter, &imagesRoutes)

		networksRouter := groupV1.Group("/networks")
		setRoutes(networksRouter, &networksRoutes)

		volumesRouter := groupV1.Group("/volumes")
		setRoutes(volumesRouter, &volumesRoutes)

		systemRouter := groupV1.Group("/system")
		setRoutes(systemRouter, &systemRoutes)

		platformRouter := groupV1.Group("/platform")
		setRoutes(platformRouter, &platformRoutes)

		stackRouter := groupV1.Group("/stack")
		setRoutes(stackRouter, &stackRouters)
	}

	return engin
}

func setRoutes(router *gin.RouterGroup, routes *Routes) {
	for _, route := range *routes {
		switch route.Method {
		case http.MethodGet:
			router.GET(route.Pattern, route.HandlerFunc)
		case http.MethodPost:
			router.POST(route.Pattern, route.HandlerFunc)
		case http.MethodPut:
			router.PUT(route.Pattern, route.HandlerFunc)
		case http.MethodDelete:
			router.DELETE(route.Pattern, route.HandlerFunc)
		}
	}
}

var platformRoutes = Routes{
	{
		"PlatformReboot",
		http.MethodPost,
		"/reboot",
		controller.PlatformReboot,
	},
	{
		"UploadLogo",
		http.MethodPost,
		"/uploadLogo",
		controller.UploadLogo,
	},
}

var systemRoutes = Routes{
	{
		"SystemDataUsage",
		http.MethodGet,
		"/df",
		controller.SystemDataUsage,
	},
}

var configsRoutes = Routes{
	{
		"ConfigCreate",
		http.MethodPost,
		"/create",
		controller.ConfigCreate,
	},

	{
		"ConfigDelete",
		http.MethodDelete,
		"/:id",
		controller.ConfigDelete,
	},

	{
		"ConfigInspect",
		http.MethodGet,
		"/:id",
		controller.ConfigInspect,
	},

	{
		"ConfigList",
		http.MethodGet,
		"",
		controller.ConfigList,
	},

	{
		"ConfigUpdate",
		http.MethodPost,
		"/:id/update",
		controller.ConfigUpdate,
	},
}

var containersRoutes = Routes{
	{
		"ContainerArchive",
		http.MethodGet,
		"/:id/archive",
		controller.ContainerArchive,
	},

	{
		"ContainerArchiveInfo",
		http.MethodHead,
		"/:id/archive",
		controller.ContainerArchiveInfo,
	},

	{
		"ContainerAttach",
		http.MethodPost,
		"/:id/attach",
		controller.ContainerAttach,
	},

	{
		"ContainerAttachWebsocket",
		http.MethodGet,
		"/:id/attach/ws",
		controller.ContainerAttachWebsocket,
	},

	{
		"ContainerChanges",
		http.MethodGet,
		"/:id/changes",
		controller.ContainerChanges,
	},

	{
		"ContainerCreate",
		http.MethodPost,
		"/create",
		controller.ContainerCreate,
	},

	{
		"ContainerDelete",
		http.MethodDelete,
		"/:id",
		controller.ContainerDelete,
	},

	{
		"ContainerExport",
		http.MethodGet,
		"/:id/export",
		controller.ContainerExport,
	},

	{
		"ContainerInspect",
		http.MethodGet,
		"/:id/json",
		controller.ContainerInspect,
	},

	{
		"ContainerNetwork",
		http.MethodGet,
		"/:id/network",
		controller.ContainerNetwork,
	},

	{
		"ContainerKill",
		http.MethodPost,
		"/:id/kill",
		controller.ContainerKill,
	},

	{
		"ContainerList",
		http.MethodGet,
		"/json",
		controller.ContainerList,
	},

	{
		"ContainerLogs",
		http.MethodGet,
		"/:id/logs",
		controller.ContainerLogs,
	},

	{
		"ContainerPause",
		http.MethodPost,
		"/:id/pause",
		controller.ContainerPause,
	},

	{
		"ContainerPrune",
		http.MethodPost,
		"/prune",
		controller.ContainerPrune,
	},

	{
		"ContainerRename",
		http.MethodPost,
		"/:id/rename",
		controller.ContainerRename,
	},

	{
		"ContainerResize",
		http.MethodPost,
		"/:id/resize",
		controller.ContainerResize,
	},

	{
		"ContainerRestart",
		http.MethodPost,
		"/:id/restart",
		controller.ContainerRestart,
	},

	{
		"ContainerStart",
		http.MethodPost,
		"/:id/start",
		controller.ContainerStart,
	},

	{
		"ContainerStats",
		http.MethodGet,
		"/:id/stats",
		controller.ContainerStats,
	},

	{
		"ContainerStop",
		http.MethodPost,
		"/:id/stop",
		controller.ContainerStop,
	},

	{
		"ContainerTop",
		http.MethodGet,
		"/:id/top",
		controller.ContainerTop,
	},

	{
		"ContainerUnpause",
		http.MethodPost,
		"/:id/unpause",
		controller.ContainerUnpause,
	},

	{
		"ContainerUpdate",
		http.MethodPost,
		"/:id/update",
		controller.ContainerUpdate,
	},

	{
		"ContainerWait",
		http.MethodPost,
		"/:id/wait",
		controller.ContainerWait,
	},

	{
		"PutContainerArchive",
		http.MethodPut,
		"/:id/archive",
		controller.PutContainerArchive,
	},

	{
		"ContainerExec",
		http.MethodPost,
		"/:id/exec",
		controller.ContainerExec,
	},

	{
		"ContainerEntrypoints",
		http.MethodGet,
		"/entrypoints",
		controller.ContainerEntrypoints,
	},
	{
		"ContainerSetDefaultRoute",
		http.MethodPut,
		"/:id/route",
		controller.ContainerSetDefaultRoute,
	},
	{
		"ContainerGetDefaultRoute",
		http.MethodGet,
		"/:id/route",
		controller.ContainerGetDefaultRoute,
	},
	{
		"ContainerServiceLog",
		http.MethodGet,
		"/:id/service/logs",
		controller.ContainerServiceLog,
	},
	{
		"ContainerBackup",
		http.MethodPost,
		"/:id/backup",
		controller.ContainerBackup,
	},
}

var execRoutes = Routes{
	{
		"ExecInspect",
		http.MethodGet,
		"/:id/json",
		controller.ExecInspect,
	},

	{
		"ExecResize",
		http.MethodPost,
		"/:id/resize",
		controller.ExecResize,
	},

	{
		"ExecStart",
		http.MethodPost,
		"/:id/start",
		controller.ExecStart,
	},
}

var buildRoutes = Routes{
	{
		"BuildPrune",
		http.MethodPost,
		"/prune",
		controller.BuildPrune,
	},

	{
		"ImageBuild",
		http.MethodPost,
		"",
		controller.ImageBuild,
	},
}

var imagesRoutes = Routes{
	{
		"ImageCreate",
		http.MethodPost,
		"/create",
		controller.ImageCreate,
	},

	{
		"ImageDelete",
		http.MethodDelete,
		"/:name",
		controller.ImageDelete,
	},

	{
		"ImageGet",
		http.MethodGet,
		"/:name/get",
		controller.ImageGet,
	},

	{
		"ImageGetAll",
		http.MethodGet,
		"/get",
		controller.ImageGetAll,
	},

	{
		"ImageHistory",
		http.MethodGet,
		"/:name/history",
		controller.ImageHistory,
	},

	{
		"ImageInspect",
		http.MethodGet,
		"/:name/json",
		controller.ImageInspect,
	},

	{
		"ImageList",
		http.MethodGet,
		"/json",
		controller.ImageList,
	},

	{
		"ImageLoad",
		http.MethodPost,
		"/load",
		controller.ImageLoad,
	},

	{
		"ImagePrune",
		http.MethodPost,
		"/prune",
		controller.ImagePrune,
	},

	{
		"ImagePush",
		http.MethodPost,
		"/:name/push",
		controller.ImagePush,
	},

	{
		"ImageSearch",
		http.MethodGet,
		"/search",
		controller.ImageSearch,
	},

	{
		"ImageTag",
		http.MethodPost,
		"/:name/tag",
		controller.ImageTag,
	},
}

var networksRoutes = Routes{
	{
		"NetworkConnect",
		http.MethodPost,
		"/:id/connect",
		controller.NetworkConnect,
	},

	{
		"NetworkCreate",
		http.MethodPost,
		"/create",
		controller.NetworkCreate,
	},

	{
		"NetworkDelete",
		http.MethodDelete,
		"/:id",
		controller.NetworkDelete,
	},

	{
		"NetworkDisconnect",
		http.MethodPost,
		"/:id/disconnect",
		controller.NetworkDisconnect,
	},

	{
		"NetworkInspect",
		http.MethodGet,
		"/:id",
		controller.NetworkInspect,
	},

	{
		"NetworkList",
		http.MethodGet,
		"",
		controller.NetworkList,
	},

	{
		"NetworkPrune",
		http.MethodPost,
		"/prune",
		controller.NetworkPrune,
	},

	{
		"NetworkInterfaces",
		http.MethodGet,
		"/interfaces",
		controller.NetworkInterfaces,
	},
}

var volumesRoutes = Routes{
	{
		"VolumeCreate",
		http.MethodPost,
		"/create",
		controller.VolumeCreate,
	},

	{
		"VolumeDelete",
		http.MethodDelete,
		"/:name",
		controller.VolumeDelete,
	},

	{
		"VolumeInspect",
		http.MethodGet,
		"/:name",
		controller.VolumeInspect,
	},

	{
		"VolumeList",
		http.MethodGet,
		"",
		controller.VolumeList,
	},

	{
		"VolumePrune",
		http.MethodPost,
		"/prune",
		controller.VolumePrune,
	},
}

var stackRouters = Routes{
	{
		"StackConf",
		http.MethodGet,
		"/config",
		controller.StackConf,
	},
	{
		"StackDeploy",
		http.MethodPost,
		"/up",
		controller.StackDeploy,
	},
	{
		"StackStart",
		http.MethodPost,
		"/start",
		controller.StackStart,
	},
	{
		"StackStop",
		http.MethodPost,
		"/stop",
		controller.StackStop,
	},
	{
		"StackRestart",
		http.MethodPost,
		"/restart",
		controller.StackRestart,
	},
	{
		"StackDown",
		http.MethodPost,
		"/down",
		controller.StackDown,
	},
	{
		"StackList",
		http.MethodGet,
		"/json",
		controller.StackList,
	},
}

// Nodes are instances of the Engine participating in a swarm. Swarm mode must be enabled for these endpoints to work.
var NodesRoutes = Routes{
	{
		"NodeDelete",
		http.MethodDelete,
		"/nodes/:id",
		controller.NodeDelete,
	},

	{
		"NodeInspect",
		http.MethodGet,
		"/nodes/:id",
		controller.NodeInspect,
	},

	{
		"NodeList",
		http.MethodGet,
		"/nodes",
		controller.NodeList,
	},

	{
		"NodeUpdate",
		http.MethodPost,
		"/nodes/:id/update",
		controller.NodeUpdate,
	},
}

// Returns information about installed plugins.
var PluginsRoutes = Routes{
	{
		"GetPluginPrivileges",
		http.MethodGet,
		"/plugins/privileges",
		controller.GetPluginPrivileges,
	},

	{
		"PluginCreate",
		http.MethodPost,
		"/plugins/create",
		controller.PluginCreate,
	},

	{
		"PluginDelete",
		http.MethodDelete,
		"/plugins/:name",
		controller.PluginDelete,
	},

	{
		"PluginDisable",
		http.MethodPost,
		"/plugins/:name/disable",
		controller.PluginDisable,
	},

	{
		"PluginEnable",
		http.MethodPost,
		"/plugins/:name/enable",
		controller.PluginEnable,
	},

	{
		"PluginInspect",
		http.MethodGet,
		"/plugins/:name/json",
		controller.PluginInspect,
	},

	{
		"PluginList",
		http.MethodGet,
		"/plugins",
		controller.PluginList,
	},

	{
		"PluginPull",
		http.MethodPost,
		"/plugins/pull",
		controller.PluginPull,
	},

	{
		"PluginPush",
		http.MethodPost,
		"/plugins/:name/push",
		controller.PluginPush,
	},

	{
		"PluginSet",
		http.MethodPost,
		"/plugins/:name/set",
		controller.PluginSet,
	},

	{
		"PluginUpgrade",
		http.MethodPost,
		"/plugins/:name/upgrade",
		controller.PluginUpgrade,
	},
}

// Secrets are sensitive data that can be used by services. Swarm mode must be enabled for these endpoints to work.
var SecretsRoutes = Routes{
	{
		"SecretCreate",
		http.MethodPost,
		"/secrets/create",
		controller.SecretCreate,
	},
	{
		"SecretDelete",
		http.MethodDelete,
		"/secrets/:id",
		controller.SecretDelete,
	},
	{
		"SecretInspect",
		http.MethodGet,
		"/secrets/:id",
		controller.SecretInspect,
	},
	{
		"SecretList",
		http.MethodGet,
		"/secrets",
		controller.SecretList,
	},
	{
		"SecretUpdate",
		http.MethodPost,
		"/secrets/:id/update",
		controller.SecretUpdate,
	},
}

// Services are the definitions of tasks to run on a swarm. Swarm mode must be enabled for these endpoints to work.
var ServiesRoutes = Routes{
	{
		"ServiceCreate",
		http.MethodPost,
		"/services/create",
		controller.ServiceCreate,
	},
	{
		"ServiceDelete",
		http.MethodDelete,
		"/services/:id",
		controller.ServiceDelete,
	},
	{
		"ServiceInspect",
		http.MethodGet,
		"/services/:id",
		controller.ServiceInspect,
	},
	{
		"ServiceList",
		http.MethodGet,
		"/services",
		controller.ServiceList,
	},
	{
		"ServiceLogs",
		http.MethodGet,
		"/services/:id/logs",
		controller.ServiceLogs,
	},
	{
		"ServiceUpdate",
		http.MethodPost,
		"/services/:id/update",
		controller.ServiceUpdate,
	},
}

var SwarmRoutes = Routes{
	{
		"SwarmInit",
		http.MethodPost,
		"/swarm/init",
		controller.SwarmInit,
	},
	{
		"SwarmInspect",
		http.MethodGet,
		"/swarm",
		controller.SwarmInspect,
	},
	{
		"SwarmJoin",
		http.MethodPost,
		"/swarm/join",
		controller.SwarmJoin,
	},
	{
		"SwarmLeave",
		http.MethodPost,
		"/swarm/leave",
		controller.SwarmLeave,
	},
	{
		"SwarmUnlock",
		http.MethodPost,
		"/swarm/unlock",
		controller.SwarmUnlock,
	},
	{
		"SwarmUnlockkey",
		http.MethodGet,
		"/swarm/unlockkey",
		controller.SwarmUnlockkey,
	},
	{
		"SwarmUpdate",
		http.MethodPost,
		"/swarm/update",
		controller.SwarmUpdate,
	},
}

var TasksRoutes = Routes{
	{
		"TaskInspect",
		http.MethodGet,
		"/tasks/:id",
		controller.TaskInspect,
	},

	{
		"TaskList",
		http.MethodGet,
		"/tasks",
		controller.TaskList,
	},

	{
		"TaskLogs",
		http.MethodGet,
		"/tasks/:id/logs",
		controller.TaskLogs,
	},
}

var UselessRoutes = Routes{
	{
		"DistributionInspect",
		http.MethodGet,
		"/distribution/:name/json",
		controller.DistributionInspect,
	},
	{
		"ImageCommit",
		http.MethodPost,
		"/commit",
		controller.ImageCommit,
	},
	{
		"Session",
		http.MethodPost,
		"/session",
		controller.Session,
	},
	{
		"SystemAuth",
		http.MethodPost,
		"/auth",
		controller.SystemAuth,
	},
	{
		"SystemEvents",
		http.MethodGet,
		"/events",
		controller.SystemEvents,
	},
	{
		"SystemInfo",
		http.MethodGet,
		"/info",
		controller.SystemInfo,
	},
	{
		"SystemPing",
		http.MethodGet,
		"/_ping",
		controller.SystemPing,
	},
	{
		"SystemPingHead",
		http.MethodHead,
		"/_ping",
		controller.SystemPingHead,
	},
}
