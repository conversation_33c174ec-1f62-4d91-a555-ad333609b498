version: "3"

services:
  service-platform:
    image: service-platform:2.7.0
    pid: host
    privileged: true
    environment:
      - HOST_PROC=/host/proc
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - data:/data
      - /proc/:/host/proc/
      - /sys:/host/sys/
      - /etc/network/interfaces.d/:/host/interfaces/
    ports:
      - "8082:8082"
      - "8083:8083"
    restart: always
    container_name: service-platform
    
volumes:
  data: