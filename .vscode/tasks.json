{
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Run Backend (Go)",
        "type": "shell",
        "command": "air", // 或者 "go run ./cmd/web/main.go"
        "options": {
          "cwd": "${workspaceFolder}/go-server"
        },
        "problemMatcher": [],
        "presentation": {
          "reveal": "always",
          "panel": "new",
          "group": "dev"
        }
      },
      {
        "label": "Run Frontend (Vue)",
        "type": "shell",
        "command": "npm run dev",
        "options": {
          "cwd": "${workspaceFolder}/web"
        },
        "problemMatcher": [],
        "presentation": {
          "reveal": "always",
          "panel": "new",
          "group": "dev"
        }
      },
      {
        "label": "Start Dev All",
        "dependsOn": [
          "Run Backend (Go)",
          "Run Frontend (Vue)"
        ],
        "dependsOrder": "parallel",
        "problemMatcher": []
      }
    ]
  }