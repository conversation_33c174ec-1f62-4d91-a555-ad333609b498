# 多架构Docker镜像构建指南

本项目支持构建 `amd64` 和 `arm64` 两种架构的Docker镜像，适用于不同的硬件平台。

## 前置要求

1. **Docker版本**: 确保Docker版本支持buildx (Docker 19.03+)
2. **启用buildx**: 确保Docker buildx功能已启用

```bash
# 检查buildx是否可用
docker buildx version

# 如果不可用，可能需要启用实验性功能
export DOCKER_CLI_EXPERIMENTAL=enabled
```

## 快速开始

### 方法一：使用构建脚本（推荐）

我们提供了便捷的构建脚本 `build-multiarch.sh`：

```bash
# 基本构建（本地）
./build-multiarch.sh

# 指定版本和镜像名
./build-multiarch.sh -n myapp -v 1.0.0

# 构建并推送到仓库
./build-multiarch.sh -n service-platform -v 2.7.0 -r registry.example.com -p

# 查看帮助
./build-multiarch.sh -h
```

#### 脚本参数说明

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| `-n` | `--name` | 镜像名称 | `service-platform` |
| `-v` | `--version` | 版本标签 | `2.7.0` |
| `-r` | `--registry` | 镜像仓库地址 | 无 |
| `-p` | `--push` | 构建后推送到仓库 | `false` |
| `-h` | `--help` | 显示帮助信息 | - |

### 方法二：手动使用Docker buildx

如果你更喜欢手动控制构建过程：

```bash
# 1. 创建并使用buildx构建器
docker buildx create --name multiarch-builder --driver docker-container --bootstrap
docker buildx use multiarch-builder

# 2. 构建多架构镜像（本地）
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --build-arg VERSION=2.7.0 \
  -t service-platform:2.7.0 \
  -f Dockerfile.supervisor \
  --load .

# 3. 构建并推送到仓库
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --build-arg VERSION=2.7.0 \
  -t registry.example.com/service-platform:2.7.0 \
  -f Dockerfile.supervisor \
  --push .
```

## 架构支持

当前支持的架构：
- `linux/amd64` - 适用于x86_64处理器
- `linux/arm64` - 适用于ARM64处理器（如Apple M1/M2、ARM服务器等）

## 构建过程说明

### 多阶段构建

1. **Go构建阶段** (`go-build`)
   - 使用 `--platform=$BUILDPLATFORM` 在构建平台上运行
   - 根据 `$TARGETPLATFORM` 自动设置 `GOARCH` 环境变量
   - 交叉编译Go应用程序

2. **Vue构建阶段** (`vue-build`)
   - 构建前端静态资源
   - 与架构无关的构建过程

3. **最终镜像阶段**
   - 基于 `debian:12`
   - 复制编译好的二进制文件和静态资源
   - 根据目标平台自动选择对应架构的docker-compose二进制文件
   - 配置运行环境

### 关键技术点

- **交叉编译**: Go程序根据目标架构进行交叉编译
- **平台参数**: 使用Docker buildx的 `TARGETPLATFORM` 和 `BUILDPLATFORM` 参数
- **自动架构检测**: 脚本自动根据目标平台设置正确的 `GOARCH`

## 使用多架构镜像

### 在不同平台运行

```bash
# Docker会自动选择匹配当前平台的镜像
docker run -d service-platform:2.7.0

# 强制指定架构
docker run -d --platform linux/amd64 service-platform:2.7.0
docker run -d --platform linux/arm64 service-platform:2.7.0
```

### 查看镜像架构信息

```bash
# 查看镜像支持的架构
docker buildx imagetools inspect service-platform:2.7.0

# 查看本地镜像架构
docker image inspect service-platform:2.7.0 | grep Architecture
```

## 故障排除

### 常见问题

1. **buildx不可用**
   ```bash
   # 启用实验性功能
   export DOCKER_CLI_EXPERIMENTAL=enabled
   
   # 或者在daemon.json中启用
   echo '{"experimental": true}' | sudo tee /etc/docker/daemon.json
   sudo systemctl restart docker
   ```

2. **构建器创建失败**
   ```bash
   # 清理现有构建器
   docker buildx rm multiarch-builder
   
   # 重新创建
   docker buildx create --name multiarch-builder --driver docker-container --bootstrap
   ```

3. **推送权限问题**
   ```bash
   # 登录到镜像仓库
   docker login registry.example.com
   ```

4. **内存不足**
   - 多架构构建需要更多内存，建议至少4GB可用内存
   - 可以考虑分别构建不同架构，然后使用manifest合并

### 调试技巧

```bash
# 查看构建器状态
docker buildx ls

# 查看构建器详细信息
docker buildx inspect multiarch-builder

# 清理构建缓存
docker buildx prune
```

## 性能优化

1. **使用构建缓存**: buildx会自动使用缓存加速构建
2. **并行构建**: 多架构会并行构建，充分利用系统资源
3. **分层优化**: Dockerfile已优化分层结构，减少重复构建

## CI/CD集成

### GitLab CI/CD

项目已配置了GitLab CI/CD流水线，支持自动化多架构镜像构建。

#### 环境变量配置

在GitLab项目设置中配置以下环境变量：

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `DOCKER_IMAGE_SERVER` | Docker镜像仓库地址 | `registry.example.com` |
| `DOCKER_USERNAME` | Docker仓库用户名 | `your-username` |
| `DOCKER_PASSWORD` | Docker仓库密码 | `your-password` |

#### 触发条件

- **自动触发**: 推送到主分支或创建标签时
- **手动触发**: 在GitLab界面手动运行流水线

#### 构建产物

- 镜像标签: `${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}`
- 最新标签: `latest`
- 支持架构: `linux/amd64`, `linux/arm64`

### GitHub Actions

项目同时提供了GitHub Actions工作流，详见 `.github/workflows/build-multiarch.yml`。

## 部署建议

1. **CI/CD集成**: 已提供GitLab CI/CD和GitHub Actions配置
2. **标签策略**: 使用语义化版本标签，如 `v1.0.0`、`latest`
3. **仓库管理**: 推荐使用支持多架构的镜像仓库（如Docker Hub、Harbor等）

## 更多信息

- [Docker Buildx文档](https://docs.docker.com/buildx/)
- [多架构镜像最佳实践](https://docs.docker.com/desktop/multi-arch/)
- [Go交叉编译指南](https://golang.org/doc/install/source#environment)